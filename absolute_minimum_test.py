"""
Absolute Minimum Performance Test
Tests the theoretical minimum time possible for auto-hold dispatch
"""

import time
import statistics
import logging
import asyncio
from typing import List, Dict
from concurrent.futures import ThreadPoolExecutor
import threading

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("absolute_minimum")


class AbsoluteMinimumTest:
    """Test the absolute theoretical minimum dispatch time"""
    
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=10, thread_name_prefix="MinTest")
    
    def test_function_call_overhead(self, iterations: int = 1000) -> Dict:
        """Test 1: Pure function call overhead"""
        logger.info(f"🔬 Testing pure function call overhead ({iterations} iterations)")
        
        def minimal_function():
            return True
        
        times = []
        for i in range(iterations):
            start = time.perf_counter()
            result = minimal_function()
            end = time.perf_counter()
            times.append((end - start) * 1000000)  # microseconds
        
        return {
            'test': 'Function Call Overhead',
            'times_us': times,
            'avg_us': statistics.mean(times),
            'min_us': min(times),
            'max_us': max(times),
            'median_us': statistics.median(times)
        }
    
    def test_thread_pool_submit(self, iterations: int = 1000) -> Dict:
        """Test 2: Thread pool submit overhead"""
        logger.info(f"⚡ Testing thread pool submit overhead ({iterations} iterations)")
        
        def minimal_work():
            return True
        
        times = []
        for i in range(iterations):
            start = time.perf_counter()
            future = self.executor.submit(minimal_work)
            end = time.perf_counter()
            times.append((end - start) * 1000)  # milliseconds
            
            # Don't wait for result, just measure submit time
        
        return {
            'test': 'Thread Pool Submit',
            'times_ms': times,
            'avg_ms': statistics.mean(times),
            'min_ms': min(times),
            'max_ms': max(times),
            'median_ms': statistics.median(times)
        }
    
    def test_data_preparation(self, iterations: int = 1000) -> Dict:
        """Test 3: Data preparation overhead"""
        logger.info(f"📦 Testing data preparation overhead ({iterations} iterations)")
        
        import json
        import secrets
        
        # Pre-prepare base data
        base_data = {
            'events': ['test-event'],
            'holdToken': 'test-token',
            'objects': [{'objectId': 'test-seat'}],
            'channelKeys': ['NO_CHANNEL'],
            'validateEventsLinkedToSameChart': True,
        }
        
        times = []
        for i in range(iterations):
            start = time.perf_counter()
            
            # Simulate minimal data prep
            data = base_data.copy()
            data['objects'] = [{'objectId': f'seat-{i}'}]
            json_str = json.dumps(data, separators=(',', ':'))
            browser_id = secrets.token_hex(8)
            
            end = time.perf_counter()
            times.append((end - start) * 1000)  # milliseconds
        
        return {
            'test': 'Data Preparation',
            'times_ms': times,
            'avg_ms': statistics.mean(times),
            'min_ms': min(times),
            'max_ms': max(times),
            'median_ms': statistics.median(times)
        }
    
    def test_combined_minimal_dispatch(self, iterations: int = 1000) -> Dict:
        """Test 4: Combined minimal dispatch (closest to real scenario)"""
        logger.info(f"🚀 Testing combined minimal dispatch ({iterations} iterations)")
        
        import json
        import secrets
        
        # Pre-prepare everything possible
        base_data = {
            'events': ['test-event'],
            'holdToken': 'test-token',
            'objects': [{'objectId': None}],
            'channelKeys': ['NO_CHANNEL'],
            'validateEventsLinkedToSameChart': True,
        }
        
        def minimal_dispatch_work(seat_id):
            # This simulates the absolute minimum work needed
            data = base_data.copy()
            data['objects'] = [{'objectId': seat_id}]
            json_str = json.dumps(data, separators=(',', ':'))
            return json_str
        
        times = []
        for i in range(iterations):
            start = time.perf_counter()
            
            # The minimal dispatch: prepare data and submit to thread pool
            seat_id = f"seat-{i}"
            future = self.executor.submit(minimal_dispatch_work, seat_id)
            
            end = time.perf_counter()
            times.append((end - start) * 1000)  # milliseconds
        
        return {
            'test': 'Combined Minimal Dispatch',
            'times_ms': times,
            'avg_ms': statistics.mean(times),
            'min_ms': min(times),
            'max_ms': max(times),
            'median_ms': statistics.median(times),
            'p95_ms': statistics.quantiles(times, n=20)[18] if len(times) >= 20 else max(times),
            'p99_ms': statistics.quantiles(times, n=100)[98] if len(times) >= 100 else max(times)
        }
    
    def test_ultra_optimized_dispatch(self, iterations: int = 1000) -> Dict:
        """Test 5: Ultra-optimized dispatch with pre-built everything"""
        logger.info(f"🏆 Testing ultra-optimized dispatch ({iterations} iterations)")
        
        import json
        
        # Pre-build absolutely everything
        pre_built_requests = []
        for i in range(iterations):
            data = {
                'events': ['test-event'],
                'holdToken': 'test-token',
                'objects': [{'objectId': f'seat-{i}'}],
                'channelKeys': ['NO_CHANNEL'],
                'validateEventsLinkedToSameChart': True,
            }
            json_str = json.dumps(data, separators=(',', ':'))
            pre_built_requests.append(json_str)
        
        def ultra_minimal_work(request_data):
            # Absolute minimum - just return the pre-built data
            return len(request_data)  # Minimal processing
        
        times = []
        for i in range(iterations):
            start = time.perf_counter()
            
            # Ultra-optimized: everything is pre-built, just submit
            future = self.executor.submit(ultra_minimal_work, pre_built_requests[i])
            
            end = time.perf_counter()
            times.append((end - start) * 1000)  # milliseconds
        
        return {
            'test': 'Ultra-Optimized Dispatch',
            'times_ms': times,
            'avg_ms': statistics.mean(times),
            'min_ms': min(times),
            'max_ms': max(times),
            'median_ms': statistics.median(times),
            'p95_ms': statistics.quantiles(times, n=20)[18] if len(times) >= 20 else max(times),
            'p99_ms': statistics.quantiles(times, n=100)[98] if len(times) >= 100 else max(times),
            'under_1ms': sum(1 for t in times if t < 1.0),
            'under_5ms': sum(1 for t in times if t < 5.0),
            'under_10ms': sum(1 for t in times if t < 10.0),
            'under_50ms': sum(1 for t in times if t < 50.0)
        }
    
    def run_all_tests(self) -> List[Dict]:
        """Run all minimum performance tests"""
        results = []
        
        # Test 1: Function call overhead
        results.append(self.test_function_call_overhead(1000))
        
        # Test 2: Thread pool submit
        results.append(self.test_thread_pool_submit(1000))
        
        # Test 3: Data preparation
        results.append(self.test_data_preparation(1000))
        
        # Test 4: Combined minimal dispatch
        results.append(self.test_combined_minimal_dispatch(1000))
        
        # Test 5: Ultra-optimized dispatch
        results.append(self.test_ultra_optimized_dispatch(1000))
        
        return results
    
    def print_results(self, results: List[Dict]):
        """Print comprehensive results"""
        logger.info("\n" + "="*80)
        logger.info("🔬 ABSOLUTE MINIMUM PERFORMANCE TEST RESULTS")
        logger.info("="*80)
        
        for result in results:
            logger.info(f"\n📊 {result['test']}:")
            
            if 'times_us' in result:
                logger.info(f"   Average: {result['avg_us']:.3f} microseconds")
                logger.info(f"   Min: {result['min_us']:.3f} microseconds")
                logger.info(f"   Max: {result['max_us']:.3f} microseconds")
                logger.info(f"   Median: {result['median_us']:.3f} microseconds")
            else:
                logger.info(f"   Average: {result['avg_ms']:.3f}ms")
                logger.info(f"   Min: {result['min_ms']:.3f}ms")
                logger.info(f"   Max: {result['max_ms']:.3f}ms")
                logger.info(f"   Median: {result['median_ms']:.3f}ms")
                
                if 'p95_ms' in result:
                    logger.info(f"   P95: {result['p95_ms']:.3f}ms")
                    logger.info(f"   P99: {result['p99_ms']:.3f}ms")
                
                if 'under_1ms' in result:
                    total = 1000  # iterations
                    logger.info(f"   Under 1ms: {result['under_1ms']}/{total} ({result['under_1ms']/total*100:.1f}%)")
                    logger.info(f"   Under 5ms: {result['under_5ms']}/{total} ({result['under_5ms']/total*100:.1f}%)")
                    logger.info(f"   Under 10ms: {result['under_10ms']}/{total} ({result['under_10ms']/total*100:.1f}%)")
                    logger.info(f"   Under 50ms: {result['under_50ms']}/{total} ({result['under_50ms']/total*100:.1f}%)")
        
        # Find absolute minimum
        dispatch_results = [r for r in results if 'times_ms' in r]
        if dispatch_results:
            best_avg = min(r['avg_ms'] for r in dispatch_results)
            best_min = min(r['min_ms'] for r in dispatch_results)
            best_test = next(r['test'] for r in dispatch_results if r['avg_ms'] == best_avg)
            
            logger.info(f"\n🏆 ABSOLUTE MINIMUM ACHIEVED:")
            logger.info(f"   Best Test: {best_test}")
            logger.info(f"   Best Average: {best_avg:.3f}ms")
            logger.info(f"   Best Single Operation: {best_min:.3f}ms")
            logger.info(f"   Theoretical Maximum RPS: {1000/best_avg:.0f} requests/second")
        
        logger.info("="*80)
    
    def cleanup(self):
        """Cleanup resources"""
        self.executor.shutdown(wait=True)


async def run_absolute_minimum_test():
    """Run the absolute minimum performance test"""
    test = AbsoluteMinimumTest()
    
    try:
        results = test.run_all_tests()
        test.print_results(results)
        return results
    finally:
        test.cleanup()


if __name__ == "__main__":
    asyncio.run(run_absolute_minimum_test())
