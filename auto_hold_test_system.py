"""
Auto Hold Test System
Comprehensive testing system for auto hold functionality with the provided event link.
"""

import asyncio
import json
import logging
import random
import time
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime

# Import required modules
from helper import (
    get_object_statuses,
    get_event_render_data,
    hold_seat,
    release_seat,
    switch_seat_immediate,
    activate_hold_token
)
from token_retrieval import get_hold_token as async_get_hold_token, cache_event_id
from webook_client import WebookClient

logger = logging.getLogger('webook_pro')

@dataclass
class TestResult:
    """Data class to store test results"""
    test_name: str
    success: bool
    duration_ms: float
    error_message: Optional[str] = None
    details: Optional[Dict[str, Any]] = None

@dataclass
class SeatInfo:
    """Data class to store seat information"""
    seat_id: str
    status: str
    category: Optional[str] = None
    price: Optional[float] = None

class AutoHoldTestSystem:
    """Comprehensive test system for auto hold functionality"""
    
    def __init__(self, event_url: str, proxy: Optional[str] = None):
        self.event_url = event_url
        self.proxy = proxy
        self.event_key = None
        self.seatsio_event_key = None
        self.chart_key = None
        self.available_seats: List[SeatInfo] = []
        self.test_results: List[TestResult] = []
        self.hold_tokens: List[str] = []
        
    async def initialize(self) -> bool:
        """Initialize the test system by loading event data"""
        try:
            logger.info(f"🔄 Initializing test system for: {self.event_url}")
            
            # Extract event key from URL
            event_key = self.event_url.split('/')[-2]
            logger.info(f"📝 Extracted event key: {event_key}")
            
            # Get event data using WebookClient
            def get_event_data():
                client = WebookClient(proxy=self.proxy)
                try:
                    return client.get_event_info(event_key=event_key, is_season=False)
                finally:
                    client.close()
            
            event_data = await asyncio.get_event_loop().run_in_executor(None, get_event_data)
            if not event_data:
                logger.error("❌ Failed to get event data")
                return False
            
            # Cache the event ID for token generation
            cache_event_id(event_data)
            
            # Extract seatsio information
            seats_io_data = event_data["data"]["seats_io"]
            self.seatsio_event_key = seats_io_data["event_key"]
            self.chart_key = seats_io_data["chart_key"]
            
            logger.info(f"🎯 Seatsio Event Key: {self.seatsio_event_key}")
            logger.info(f"🗺️ Chart Key: {self.chart_key}")
            
            # Get seat statuses
            seats = await get_object_statuses(self.seatsio_event_key, self.chart_key, proxy=self.proxy)
            
            # Filter available seats
            self.available_seats = []
            for seat in seats:
                if isinstance(seat, dict) and seat.get('status') == 'free':
                    seat_info = SeatInfo(
                        seat_id=seat.get('id', ''),
                        status=seat.get('status', ''),
                        category=seat.get('category', ''),
                        price=seat.get('pricing', {}).get('price') if seat.get('pricing') else None
                    )
                    self.available_seats.append(seat_info)
            
            logger.info(f"✅ Found {len(self.available_seats)} available seats")
            return len(self.available_seats) > 0
            
        except Exception as e:
            logger.error(f"❌ Error initializing test system: {str(e)}")
            return False
    
    async def test_token_generation(self, count: int = 5) -> TestResult:
        """Test hold token generation"""
        start_time = time.time()
        try:
            logger.info(f"🔑 Testing token generation ({count} tokens)...")
            
            # Get event ID from cache
            from token_retrieval import get_cached_event_id
            event_id = get_cached_event_id()
            if not event_id:
                return TestResult("Token Generation", False, 0, "No cached event ID")
            
            # Generate tokens
            tokens = []
            for i in range(count):
                token = await async_get_hold_token(event_id=event_id, proxy=self.proxy)
                if token:
                    tokens.append(token)
                    logger.info(f"✅ Generated token {i+1}/{count}: {token[:8]}...")
                else:
                    logger.warning(f"⚠️ Failed to generate token {i+1}/{count}")
            
            self.hold_tokens.extend(tokens)
            duration_ms = (time.time() - start_time) * 1000
            
            success = len(tokens) >= count * 0.8  # 80% success rate
            return TestResult(
                "Token Generation", 
                success, 
                duration_ms,
                None if success else f"Only generated {len(tokens)}/{count} tokens",
                {"tokens_generated": len(tokens), "tokens_requested": count}
            )
            
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            return TestResult("Token Generation", False, duration_ms, str(e))
    
    async def test_seat_hold(self, seat_count: int = 3) -> TestResult:
        """Test holding seats"""
        start_time = time.time()
        try:
            logger.info(f"🪑 Testing seat hold ({seat_count} seats)...")
            
            if len(self.available_seats) < seat_count:
                return TestResult("Seat Hold", False, 0, f"Not enough available seats ({len(self.available_seats)} < {seat_count})")
            
            if len(self.hold_tokens) < seat_count:
                return TestResult("Seat Hold", False, 0, f"Not enough hold tokens ({len(self.hold_tokens)} < {seat_count})")
            
            # Select random seats and tokens
            test_seats = random.sample(self.available_seats, seat_count)
            test_tokens = self.hold_tokens[:seat_count]
            
            successful_holds = 0
            for i, (seat, token) in enumerate(zip(test_seats, test_tokens)):
                logger.info(f"🎯 Holding seat {i+1}/{seat_count}: {seat.seat_id}")
                success = await hold_seat(
                    seat_number=seat.seat_id,
                    event_key=self.seatsio_event_key,
                    hold_token=token,
                    proxy=self.proxy
                )
                if success:
                    successful_holds += 1
                    logger.info(f"✅ Successfully held seat: {seat.seat_id}")
                else:
                    logger.warning(f"⚠️ Failed to hold seat: {seat.seat_id}")
            
            duration_ms = (time.time() - start_time) * 1000
            success = successful_holds >= seat_count * 0.8  # 80% success rate
            
            return TestResult(
                "Seat Hold",
                success,
                duration_ms,
                None if success else f"Only held {successful_holds}/{seat_count} seats",
                {"seats_held": successful_holds, "seats_requested": seat_count}
            )
            
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            return TestResult("Seat Hold", False, duration_ms, str(e))
    
    async def test_seat_switch(self, switch_count: int = 2) -> TestResult:
        """Test switching seats (release + hold)"""
        start_time = time.time()
        try:
            logger.info(f"🔄 Testing seat switch ({switch_count} switches)...")
            
            if len(self.available_seats) < switch_count * 2:
                return TestResult("Seat Switch", False, 0, f"Not enough available seats for switching")
            
            if len(self.hold_tokens) < switch_count * 2:
                return TestResult("Seat Switch", False, 0, f"Not enough hold tokens for switching")
            
            successful_switches = 0
            for i in range(switch_count):
                # Select two different seats and tokens
                seat1 = random.choice(self.available_seats)
                seat2 = random.choice([s for s in self.available_seats if s.seat_id != seat1.seat_id])
                old_token = self.hold_tokens[i * 2]
                new_token = self.hold_tokens[i * 2 + 1]
                
                logger.info(f"🔄 Switch {i+1}/{switch_count}: {seat1.seat_id} -> {seat2.seat_id}")
                
                # First hold the initial seat
                hold_success = await hold_seat(
                    seat_number=seat1.seat_id,
                    event_key=self.seatsio_event_key,
                    hold_token=old_token,
                    proxy=self.proxy
                )
                
                if hold_success:
                    # Now switch to the new seat
                    switch_success = await switch_seat_immediate(
                        seat_number=seat2.seat_id,
                        event_key=self.seatsio_event_key,
                        old_token=old_token,
                        new_token=new_token,
                        proxy=self.proxy
                    )
                    
                    if switch_success:
                        successful_switches += 1
                        logger.info(f"✅ Successfully switched: {seat1.seat_id} -> {seat2.seat_id}")
                    else:
                        logger.warning(f"⚠️ Failed to switch: {seat1.seat_id} -> {seat2.seat_id}")
                else:
                    logger.warning(f"⚠️ Failed to hold initial seat: {seat1.seat_id}")
            
            duration_ms = (time.time() - start_time) * 1000
            success = successful_switches >= switch_count * 0.8  # 80% success rate
            
            return TestResult(
                "Seat Switch",
                success,
                duration_ms,
                None if success else f"Only switched {successful_switches}/{switch_count} seats",
                {"switches_successful": successful_switches, "switches_requested": switch_count}
            )
            
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            return TestResult("Seat Switch", False, duration_ms, str(e))
    
    async def test_token_activation(self) -> TestResult:
        """Test token activation"""
        start_time = time.time()
        try:
            logger.info("⚡ Testing token activation...")
            
            if not self.hold_tokens:
                return TestResult("Token Activation", False, 0, "No hold tokens available")
            
            # Test activating a few tokens
            test_token = self.hold_tokens[0]
            logger.info(f"⚡ Activating token: {test_token[:8]}...")
            
            time_left = await activate_hold_token(test_token, proxy=self.proxy)
            
            duration_ms = (time.time() - start_time) * 1000
            success = time_left > 0
            
            return TestResult(
                "Token Activation",
                success,
                duration_ms,
                None if success else "Token activation returned 0 or negative time",
                {"time_left_seconds": time_left}
            )
            
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            return TestResult("Token Activation", False, duration_ms, str(e))
    
    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run all tests and return comprehensive results"""
        logger.info("🚀 Starting comprehensive auto hold test suite...")
        
        # Initialize
        if not await self.initialize():
            return {"error": "Failed to initialize test system"}
        
        # Run tests
        tests = [
            ("Token Generation", self.test_token_generation(5)),
            ("Seat Hold", self.test_seat_hold(3)),
            ("Seat Switch", self.test_seat_switch(2)),
            ("Token Activation", self.test_token_activation())
        ]
        
        results = {}
        for test_name, test_coro in tests:
            logger.info(f"🧪 Running test: {test_name}")
            result = await test_coro
            results[test_name] = result
            self.test_results.append(result)
            
            if result.success:
                logger.info(f"✅ {test_name}: PASSED ({result.duration_ms:.2f}ms)")
            else:
                logger.error(f"❌ {test_name}: FAILED - {result.error_message}")
        
        # Generate summary
        total_tests = len(results)
        passed_tests = sum(1 for r in results.values() if r.success)
        
        summary = {
            "event_url": self.event_url,
            "seatsio_event_key": self.seatsio_event_key,
            "chart_key": self.chart_key,
            "available_seats_count": len(self.available_seats),
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "success_rate": (passed_tests / total_tests) * 100 if total_tests > 0 else 0,
            "test_results": {name: {
                "success": result.success,
                "duration_ms": result.duration_ms,
                "error": result.error_message,
                "details": result.details
            } for name, result in results.items()},
            "timestamp": datetime.now().isoformat()
        }
        
        logger.info(f"📊 Test Summary: {passed_tests}/{total_tests} tests passed ({summary['success_rate']:.1f}%)")
        
        return summary

# Convenience function to run tests
async def run_auto_hold_test(event_url: str, proxy: Optional[str] = None) -> Dict[str, Any]:
    """Run the auto hold test system"""
    test_system = AutoHoldTestSystem(event_url, proxy)
    return await test_system.run_comprehensive_test()
