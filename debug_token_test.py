#!/usr/bin/env python3
"""
Debug script to test token generation
"""

import asyncio
import logging
import inspect

# Import warning suppression first
import suppress_warnings  # noqa: F401

from logger_setup import setup_logger
from token_retrieval import get_hold_token, get_cached_event_id, cache_event_id
from webook_client import WebookClient

# Setup logging
logger = setup_logger()
logger.setLevel(logging.INFO)

async def debug_token_generation():
    """Debug token generation"""
    print("🔧 Debug Token Generation Test")
    
    # Test event
    event_url = "https://webook.com/ar/events/moc-tar-festival-final-night-ibrahim-al-hakami-hams-fekri/book"
    event_key = event_url.split('/')[-2]
    proxy = "p.webshare.io:80:ucegtvkm-rotate:mcg512cjss6l"
    
    print(f"📝 Event key: {event_key}")
    print(f"🌐 Proxy: {proxy}")
    
    # Get event data to cache event ID
    def get_event_data():
        client = WebookClient(proxy=proxy)
        try:
            return client.get_event_info(event_key=event_key, is_season=False)
        finally:
            client.close()
    
    event_data = await asyncio.get_event_loop().run_in_executor(None, get_event_data)
    if not event_data:
        print("❌ Failed to get event data")
        return
    
    # Cache the event ID
    cache_event_id(event_data)
    event_id = get_cached_event_id()
    print(f"🆔 Event ID: {event_id}")
    
    # Check the function signature
    print(f"🔍 get_hold_token function: {get_hold_token}")
    print(f"🔍 Is coroutine function: {inspect.iscoroutinefunction(get_hold_token)}")
    print(f"🔍 Function signature: {inspect.signature(get_hold_token)}")
    
    # Try to call the function
    try:
        print("🔑 Calling get_hold_token...")
        result = get_hold_token(event_id=event_id, proxy=proxy)
        print(f"🔍 Result type: {type(result)}")
        print(f"🔍 Result: {result}")
        
        if inspect.iscoroutine(result):
            print("✅ Result is a coroutine, awaiting...")
            token = await result
            print(f"🎯 Token: {token}")
        else:
            print(f"⚠️ Result is not a coroutine: {result}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_token_generation())
