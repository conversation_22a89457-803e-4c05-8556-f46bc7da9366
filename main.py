# main.py
# Import warning suppression first
import suppress_warnings  # noqa: F401

import sys
from PyQt5.QtWidgets import QApplication
from logger_setup import setup_logger
from elitesoftworks import print_logo
from main_window import MainWindow
from script_info import SCRIPT_NAME, VERSION
import logging
import traceback

DEBUG = False
logging.basicConfig(level=logging.INFO)
logging.getLogger().setLevel(logging.INFO)
def main():
    try:
        # Initialize the logger
        logger = setup_logger()
        if DEBUG:
            logger.setLevel(logging.DEBUG)
        else:
            logger.setLevel(logging.INFO)
        print_logo(script_name=SCRIPT_NAME, version=VERSION)

        app = QApplication(sys.argv)
        app.setOrganizationName("Elitesoftworks")
        app.setApplicationName("webookBookingPro")

        window = MainWindow()
        window.show()
        sys.exit(app.exec_())
    except Exception as e:
        print(f"An error occurred: {e}")
        traceback.print_exc()
        logger.critical(f"An error occurred: {e}")
        logger.critical(traceback.format_exc())
        sys.exit(1)



if __name__ == "__main__":
    main()
