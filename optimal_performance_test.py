"""
Optimal Performance Test - Find the absolute fastest time we can achieve
Tests different optimization levels to find the best possible performance
"""

import asyncio
import logging
import time
import statistics
import threading
from typing import List, Dict, Any, Optional
from ultra_fast_hold_manager import UltraFastHoldManager
from token_retrieval import get_hold_token, get_cached_event_id

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("optimal_test")


class OptimalPerformanceTest:
    """Test system to find the absolute optimal performance"""
    
    def __init__(self, event_key: str, chart_key: str, channel_keys: List[str], 
                 team_id: str, proxy: Optional[str] = None, event_id: Optional[str] = None):
        self.event_key = event_key
        self.chart_key = chart_key
        self.channel_keys = channel_keys
        self.team_id = team_id
        self.proxy = proxy
        self.event_id = event_id
    
    async def test_queue_time_only(self, iterations: int = 100) -> Dict[str, Any]:
        """Test 1: Pure queue time - how fast can we dispatch requests?"""
        logger.info(f"🚀 TEST 1: Queue Time Only ({iterations} iterations)")
        
        manager = UltraFastHoldManager(
            event_key=self.event_key,
            chart_key=self.chart_key,
            channel_keys=self.channel_keys,
            team_id=self.team_id,
            proxy=self.proxy,
            event_id=self.event_id
        )
        
        await asyncio.sleep(2.0)  # Wait for token
        
        queue_times = []
        test_seat = "Silver B8-P-16"
        
        for i in range(iterations):
            start_time = time.perf_counter()  # High precision timer
            success = manager.hold_seat_ultra_fast(test_seat)
            queue_time = (time.perf_counter() - start_time) * 1000.0
            
            queue_times.append(queue_time)
            
            if i % 25 == 0:
                avg = statistics.mean(queue_times[-25:]) if len(queue_times) >= 25 else statistics.mean(queue_times)
                logger.info(f"   Progress {i+1}/{iterations}: Avg queue time: {avg:.3f}ms")
            
            # Minimal delay
            await asyncio.sleep(0.001)
        
        manager.cleanup()
        
        return {
            'test_name': 'Queue Time Only',
            'iterations': iterations,
            'times': queue_times,
            'avg_ms': statistics.mean(queue_times),
            'median_ms': statistics.median(queue_times),
            'min_ms': min(queue_times),
            'max_ms': max(queue_times),
            'p95_ms': statistics.quantiles(queue_times, n=20)[18],
            'p99_ms': statistics.quantiles(queue_times, n=100)[98],
            'under_1ms': sum(1 for t in queue_times if t < 1.0),
            'under_10ms': sum(1 for t in queue_times if t < 10.0),
            'under_50ms': sum(1 for t in queue_times if t < 50.0),
            'under_100ms': sum(1 for t in queue_times if t < 100.0)
        }
    
    async def test_minimal_overhead(self, iterations: int = 50) -> Dict[str, Any]:
        """Test 2: Minimal overhead version - stripped down to essentials"""
        logger.info(f"🔥 TEST 2: Minimal Overhead ({iterations} iterations)")
        
        # Pre-initialize everything possible
        import json
        import secrets
        from helper import build_channel_keys
        from chart_token_manager import generate_x_signature
        
        # Get token once
        event_id = self.event_id or get_cached_event_id()
        token = await get_hold_token(event_id, proxy=self.proxy)
        
        if not token:
            return {'error': 'Failed to get token'}
        
        # Pre-build everything
        built_channel_keys = build_channel_keys(self.channel_keys, self.team_id)
        test_seat = "Silver B8-P-16"
        
        base_request = {
            'events': [self.event_key],
            'holdToken': token,
            'objects': [{'objectId': test_seat}],
            'channelKeys': built_channel_keys,
            'validateEventsLinkedToSameChart': True,
        }
        
        body_str = json.dumps(base_request, separators=(',', ':'))
        browser_id = secrets.token_hex(8)
        
        headers = {
            'Host': 'cdn-eu.seatsio.net',
            'Accept': '*/*',
            'Content-Type': 'application/json',
            'X-Client-Tool': 'Renderer',
            'X-Browser-Id': browser_id,
            'X-Signature': generate_x_signature(body_str),
            'Origin': 'https://cdn-eu.seatsio.net',
        }
        
        minimal_times = []
        
        for i in range(iterations):
            start_time = time.perf_counter()
            
            # Minimal operation - just the essential dispatch
            def minimal_dispatch():
                # Simulate the absolute minimum work: just queue to thread pool
                import concurrent.futures
                executor = concurrent.futures.ThreadPoolExecutor(max_workers=1)
                future = executor.submit(lambda: None)  # Minimal work
                return future

            future = minimal_dispatch()
            
            dispatch_time = (time.perf_counter() - start_time) * 1000.0
            minimal_times.append(dispatch_time)
            
            if i % 10 == 0:
                avg = statistics.mean(minimal_times[-10:]) if len(minimal_times) >= 10 else statistics.mean(minimal_times)
                logger.info(f"   Progress {i+1}/{iterations}: Avg minimal time: {avg:.3f}ms")
        
        return {
            'test_name': 'Minimal Overhead',
            'iterations': iterations,
            'times': minimal_times,
            'avg_ms': statistics.mean(minimal_times),
            'median_ms': statistics.median(minimal_times),
            'min_ms': min(minimal_times),
            'max_ms': max(minimal_times),
            'p95_ms': statistics.quantiles(minimal_times, n=20)[18] if len(minimal_times) >= 20 else max(minimal_times),
            'p99_ms': statistics.quantiles(minimal_times, n=100)[98] if len(minimal_times) >= 100 else max(minimal_times),
            'under_1ms': sum(1 for t in minimal_times if t < 1.0),
            'under_10ms': sum(1 for t in minimal_times if t < 10.0),
            'under_50ms': sum(1 for t in minimal_times if t < 50.0),
            'under_100ms': sum(1 for t in minimal_times if t < 100.0)
        }
    
    async def test_burst_performance(self, burst_size: int = 10, num_bursts: int = 5) -> Dict[str, Any]:
        """Test 3: Burst performance - how fast can we handle multiple rapid requests?"""
        logger.info(f"⚡ TEST 3: Burst Performance ({num_bursts} bursts of {burst_size} requests)")
        
        manager = UltraFastHoldManager(
            event_key=self.event_key,
            chart_key=self.chart_key,
            channel_keys=self.channel_keys,
            team_id=self.team_id,
            proxy=self.proxy,
            event_id=self.event_id
        )
        
        await asyncio.sleep(2.0)
        
        burst_times = []
        individual_times = []
        test_seat = "Silver B8-P-16"
        
        for burst in range(num_bursts):
            logger.info(f"   Running burst {burst+1}/{num_bursts}")
            
            burst_start = time.perf_counter()
            
            for i in range(burst_size):
                start_time = time.perf_counter()
                manager.hold_seat_ultra_fast(f"{test_seat}-{i}")
                individual_time = (time.perf_counter() - start_time) * 1000.0
                individual_times.append(individual_time)
            
            burst_total_time = (time.perf_counter() - burst_start) * 1000.0
            burst_times.append(burst_total_time)
            
            logger.info(f"   Burst {burst+1} completed in {burst_total_time:.1f}ms")
            
            # Small delay between bursts
            await asyncio.sleep(0.1)
        
        manager.cleanup()
        
        return {
            'test_name': 'Burst Performance',
            'burst_size': burst_size,
            'num_bursts': num_bursts,
            'burst_times': burst_times,
            'individual_times': individual_times,
            'avg_burst_ms': statistics.mean(burst_times),
            'avg_individual_ms': statistics.mean(individual_times),
            'min_individual_ms': min(individual_times),
            'max_individual_ms': max(individual_times),
            'requests_per_second': (burst_size * num_bursts) / (sum(burst_times) / 1000.0),
            'under_1ms_individual': sum(1 for t in individual_times if t < 1.0),
            'under_10ms_individual': sum(1 for t in individual_times if t < 10.0),
            'under_50ms_individual': sum(1 for t in individual_times if t < 50.0)
        }
    
    def print_results(self, results: List[Dict[str, Any]]):
        """Print comprehensive results"""
        logger.info("\n" + "="*80)
        logger.info("🏆 OPTIMAL PERFORMANCE TEST RESULTS")
        logger.info("="*80)
        
        for result in results:
            if 'error' in result:
                logger.error(f"❌ {result.get('test_name', 'Unknown')}: {result['error']}")
                continue
                
            logger.info(f"\n📊 {result['test_name']}:")
            logger.info(f"   Iterations: {result.get('iterations', 'N/A')}")
            logger.info(f"   Average: {result['avg_ms']:.3f}ms")
            logger.info(f"   Median: {result['median_ms']:.3f}ms")
            logger.info(f"   Min: {result['min_ms']:.3f}ms")
            logger.info(f"   Max: {result['max_ms']:.3f}ms")
            logger.info(f"   P95: {result['p95_ms']:.3f}ms")
            
            if 'p99_ms' in result:
                logger.info(f"   P99: {result['p99_ms']:.3f}ms")
            
            logger.info(f"   Under 1ms: {result['under_1ms']}/{result.get('iterations', 0)} ({result['under_1ms']/result.get('iterations', 1)*100:.1f}%)")
            logger.info(f"   Under 10ms: {result['under_10ms']}/{result.get('iterations', 0)} ({result['under_10ms']/result.get('iterations', 1)*100:.1f}%)")
            logger.info(f"   Under 50ms: {result['under_50ms']}/{result.get('iterations', 0)} ({result['under_50ms']/result.get('iterations', 1)*100:.1f}%)")
            logger.info(f"   Under 100ms: {result['under_100ms']}/{result.get('iterations', 0)} ({result['under_100ms']/result.get('iterations', 1)*100:.1f}%)")
            
            if 'requests_per_second' in result:
                logger.info(f"   Requests/sec: {result['requests_per_second']:.1f}")
        
        # Find the best performance
        best_avg = min(r['avg_ms'] for r in results if 'error' not in r)
        best_min = min(r['min_ms'] for r in results if 'error' not in r)
        
        logger.info(f"\n🎯 OPTIMAL PERFORMANCE ACHIEVED:")
        logger.info(f"   Best Average: {best_avg:.3f}ms")
        logger.info(f"   Best Single Request: {best_min:.3f}ms")
        logger.info("="*80)


async def run_optimal_performance_test(event_key: str, chart_key: str, channel_keys: List[str], 
                                     team_id: str, proxy: Optional[str] = None, 
                                     event_id: Optional[str] = None) -> List[Dict[str, Any]]:
    """Run all optimal performance tests"""
    
    test_system = OptimalPerformanceTest(
        event_key=event_key,
        chart_key=chart_key,
        channel_keys=channel_keys,
        team_id=team_id,
        proxy=proxy,
        event_id=event_id
    )
    
    results = []
    
    # Test 1: Queue time only
    try:
        result1 = await test_system.test_queue_time_only(100)
        results.append(result1)
    except Exception as e:
        results.append({'test_name': 'Queue Time Only', 'error': str(e)})
    
    # Test 2: Minimal overhead
    try:
        result2 = await test_system.test_minimal_overhead(50)
        results.append(result2)
    except Exception as e:
        results.append({'test_name': 'Minimal Overhead', 'error': str(e)})
    
    # Test 3: Burst performance
    try:
        result3 = await test_system.test_burst_performance(10, 5)
        results.append(result3)
    except Exception as e:
        results.append({'test_name': 'Burst Performance', 'error': str(e)})
    
    # Print results
    test_system.print_results(results)
    
    return results


if __name__ == "__main__":
    # Example usage - replace with your actual event data
    async def main():
        event_key = "moc-tar-festival-final-night-ibrahim-al-hakami-hams-fekri"
        chart_key = "your_chart_key"
        channel_keys = ["NO_CHANNEL"]
        team_id = "default"
        proxy = None
        event_id = None
        
        await run_optimal_performance_test(
            event_key=event_key,
            chart_key=chart_key,
            channel_keys=channel_keys,
            team_id=team_id,
            proxy=proxy,
            event_id=event_id
        )
    
    asyncio.run(main())
