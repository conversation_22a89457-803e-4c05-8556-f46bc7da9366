#!/usr/bin/env python3
"""
Auto Hold Test Runner
Simple script to run the auto hold test system with the provided event URL.
"""

import asyncio
import json
import logging
import sys
from datetime import datetime

# Import warning suppression first
import suppress_warnings  # noqa: F401

from auto_hold_test_system import run_auto_hold_test
from logger_setup import setup_logger

# Setup logging
logger = setup_logger()
logger.setLevel(logging.INFO)

# Test configuration
EVENT_URL = "https://webook.com/ar/events/moc-tar-festival-final-night-ibrahim-al-hakami-hams-fekri/book"
PROXY = "p.webshare.io:80:ucegtvkm-rotate:mcg512cjss6l"  # Set to None to disable proxy

async def main():
    """Main test runner function"""
    print("=" * 80)
    print("🚀 AUTO HOLD TEST SYSTEM")
    print("=" * 80)
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🔗 Event URL: {EVENT_URL}")
    print(f"🌐 Proxy: {PROXY if PROXY else 'Disabled'}")
    print("=" * 80)
    
    try:
        # Run the comprehensive test
        logger.info("🚀 Starting auto hold test system...")
        results = await run_auto_hold_test(EVENT_URL, PROXY)
        
        # Check if there was an initialization error
        if "error" in results:
            print(f"❌ Test failed to initialize: {results['error']}")
            return 1
        
        # Print detailed results
        print("\n📊 TEST RESULTS:")
        print("-" * 50)
        
        for test_name, test_result in results["test_results"].items():
            status = "✅ PASSED" if test_result["success"] else "❌ FAILED"
            duration = test_result["duration_ms"]
            print(f"{test_name:20} | {status:10} | {duration:8.2f}ms")
            
            if not test_result["success"] and test_result["error"]:
                print(f"                     | Error: {test_result['error']}")
            
            if test_result["details"]:
                for key, value in test_result["details"].items():
                    print(f"                     | {key}: {value}")
            print()
        
        # Print summary
        print("-" * 50)
        print(f"📈 SUMMARY:")
        print(f"   Total Tests: {results['total_tests']}")
        print(f"   Passed: {results['passed_tests']}")
        print(f"   Success Rate: {results['success_rate']:.1f}%")
        print(f"   Available Seats: {results['available_seats_count']}")
        print(f"   Seatsio Event: {results['seatsio_event_key']}")
        
        # Save results to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"auto_hold_test_results_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Results saved to: {filename}")
        
        # Return appropriate exit code
        if results['success_rate'] >= 80:
            print("\n🎉 Overall test result: SUCCESS")
            return 0
        else:
            print("\n⚠️ Overall test result: PARTIAL SUCCESS")
            return 1
            
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
        return 130
    except Exception as e:
        logger.error(f"❌ Unexpected error during testing: {str(e)}")
        print(f"\n❌ Test failed with error: {str(e)}")
        return 1

def run_quick_test():
    """Run a quick test without full async setup"""
    print("🔧 Quick connectivity test...")
    
    try:
        from webook_client import WebookClient
        
        # Extract event key
        event_key = EVENT_URL.split('/')[-2]
        print(f"📝 Event key: {event_key}")
        
        # Test basic connectivity
        client = WebookClient(proxy=PROXY)
        try:
            event_data = client.get_event_info(event_key=event_key, is_season=False)
            if event_data:
                print("✅ Basic connectivity: OK")
                seats_io = event_data.get("data", {}).get("seats_io", {})
                print(f"🎯 Seatsio Event: {seats_io.get('event_key', 'N/A')}")
                print(f"🗺️ Chart Key: {seats_io.get('chart_key', 'N/A')}")
                return True
            else:
                print("❌ Basic connectivity: FAILED")
                return False
        finally:
            client.close()
            
    except Exception as e:
        print(f"❌ Quick test failed: {str(e)}")
        return False

if __name__ == "__main__":
    # Check command line arguments
    if len(sys.argv) > 1 and sys.argv[1] == "--quick":
        # Run quick test
        success = run_quick_test()
        sys.exit(0 if success else 1)
    else:
        # Run full test
        try:
            exit_code = asyncio.run(main())
            sys.exit(exit_code)
        except KeyboardInterrupt:
            print("\n⏹️ Test interrupted")
            sys.exit(130)
        except Exception as e:
            print(f"❌ Failed to run test: {str(e)}")
            sys.exit(1)
