"""
Ultra-Fast Hold Manager - Optimized for <100ms response times
Eliminates all performance bottlenecks with persistent connections and minimal overhead
"""

import asyncio
import logging
import time
import threading
import json
import secrets
import httpx
from typing import Optional, List, Dict, Any
from PyQt5.QtCore import QObject, pyqtSignal
from concurrent.futures import ThreadPoolExecutor
from collections import deque

from helper import build_channel_keys
from token_retrieval import get_hold_token, get_cached_event_id
from chart_token_manager import generate_x_signature

logger = logging.getLogger("webook_pro")


class SimpleHoldManager(QObject):
    """
    A simplified seat holding manager that uses the existing async functions
    from helper.py instead of reimplementing everything.
    """
    
    # Signals
    seat_held_signal = pyqtSignal(str, str)  # seat_id, token_id
    seat_released_signal = pyqtSignal(str)   # seat_id
    log_signal = pyqtSignal(str)
    performance_signal = pyqtSignal(dict)
    
    def __init__(self, event_key: str, chart_key: str, channel_keys: List[str], 
                 team_id: str, proxy: Optional[str] = None, event_id: Optional[str] = None):
        super().__init__()
        
        self.event_key = event_key
        self.chart_key = chart_key
        self.channel_keys = channel_keys or ['NO_CHANNEL']
        self.team_id = team_id
        self.proxy = proxy
        self.event_id = event_id
        
        # Current hold token
        self.current_token = None
        self.token_expire_time = None
        
        # Performance tracking
        self.performance_stats = {
            'holds_attempted': 0,
            'holds_successful': 0,
            'average_response_time': 0.0,
            'last_response_times': []
        }
        
        # Initialize with a token
        self._initialize_token()
    
    def _initialize_token(self):
        """Get an initial hold token"""
        try:
            # Run async token retrieval in a separate thread to avoid event loop conflicts
            import concurrent.futures

            async def get_token_task():
                event_id = self.event_id or get_cached_event_id()
                if event_id:
                    return await get_hold_token(event_id=event_id, proxy=self.proxy)
                return None

            # Use thread pool executor to run async function safely
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, get_token_task())
                self.current_token = future.result()

            if self.current_token:
                self.token_expire_time = time.time() + (10 * 60)  # 10 minutes
                self.log_signal.emit(f"✅ Initialized with hold token: {self.current_token[:8]}...")
            else:
                self.log_signal.emit("⚠️ Failed to get initial hold token")
        except Exception as e:
            self.log_signal.emit(f"❌ Error initializing token: {str(e)}")
            logger.error(f"Error initializing token: {str(e)}")
    
    def _ensure_valid_token(self) -> bool:
        """Ensure we have a valid, non-expired token"""
        if not self.current_token or (self.token_expire_time and time.time() > self.token_expire_time):
            self.log_signal.emit("🔄 Token expired, getting new token...")
            self._initialize_token()
        
        return self.current_token is not None
    
    def hold_seat_async(self, seat_id: str) -> bool:
        """
        Queue a hold for a seat using an isolated async HTTP client.
        Returns immediately (True if queued). Detailed result is logged when done.
        """
        if not self._ensure_valid_token():
            self.log_signal.emit(f"❌ No valid token available to hold seat {seat_id}")
            return False

        try:
            start_time = time.time()

            # Fire-and-forget worker to keep UI thread under ~100ms total work
            def run_hold_seat():
                try:
                    # Create a completely isolated event loop and HTTP client in this thread
                    import asyncio
                    import httpx
                    import json
                    import secrets
                    from chart_token_manager import generate_x_signature as central_generate_x_signature

                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        async def isolated_hold_seat():
                            url = (
                                'https://34.102.211.90/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76'
                                '/events/groups/actions/hold-objects'
                            )

                            built_channel_keys = build_channel_keys(self.channel_keys, self.team_id)

                            payload = {
                                'events': [self.event_key],
                                'holdToken': self.current_token,
                                'objects': [{'objectId': seat_id}],
                                'channelKeys': built_channel_keys,
                                'validateEventsLinkedToSameChart': True,
                            }
                            body_str = json.dumps(payload, separators=(',', ':'))

                            headers = {
                                'Host': 'cdn-eu.seatsio.net',
                                'accept': '*/*',
                                'content-type': 'application/json',
                                'origin': 'https://cdn-eu.seatsio.net',
                                'x-client-tool': 'Renderer',
                                'x-browser-id': secrets.token_hex(8),
                                'x-signature': central_generate_x_signature(body_str),
                            }

                            client_kwargs = {
                                'timeout': httpx.Timeout(10.0),
                                'verify': False,
                            }
                            if self.proxy:
                                parts = self.proxy.split(':')
                                if len(parts) == 4:
                                    client_kwargs['proxy'] = f"http://{parts[2]}:{parts[3]}@{parts[0]}:{parts[1]}"

                            async with httpx.AsyncClient(**client_kwargs) as client:
                                resp = await client.post(url, content=body_str, headers=headers)
                                info = {
                                    'status': resp.status_code,
                                    'text': (resp.text or '')[:200],
                                }
                                return resp.status_code == 204, info

                        success, info = loop.run_until_complete(isolated_hold_seat())
                    finally:
                        loop.close()

                    # Track performance
                    response_time = (time.time() - start_time) * 1000.0
                    self._update_performance_stats(success, response_time)

                    if success:
                        # Emit results
                        self.seat_held_signal.emit(seat_id, self.current_token)
                        self.log_signal.emit(
                            f"✅ Successfully held seat {seat_id} in {response_time:.1f}ms"
                        )
                    else:
                        # Add detailed diagnostics on failure
                        status = info.get('status')
                        text = info.get('text', '')
                        token_pref = (self.current_token or '')[:8]
                        chan = build_channel_keys(self.channel_keys, self.team_id)
                        self.log_signal.emit(
                            f"❌ Failed to hold seat {seat_id} | status={status} | token={token_pref}... | channels={chan} | {text}"
                        )
                except Exception as e:
                    self.log_signal.emit(f"❌ Error holding seat {seat_id}: {str(e)}")
                    logger.error(f"Error holding seat {seat_id}: {str(e)}")

            import threading
            threading.Thread(target=run_hold_seat, daemon=True).start()
            return True

        except Exception as e:
            self.log_signal.emit(f"❌ Error queuing hold for seat {seat_id}: {str(e)}")
            logger.error(f"Error queuing hold for seat {seat_id}: {str(e)}")
            return False
    
    def release_seat_async(self, seat_id: str, token: Optional[str] = None) -> bool:
        """
        Release a seat using the async helper function
        """
        release_token = token or self.current_token
        if not release_token:
            self.log_signal.emit(f"❌ No token available to release seat {seat_id}")
            return False
        
        try:
            start_time = time.time()
            
            # Run the async release_seat function in a separate thread to avoid event loop conflicts
            import concurrent.futures

            async def release_seat_task():
                return await release_seat(
                    seat_number=seat_id,
                    event_key=self.event_key,
                    hold_token=release_token,
                    proxy=self.proxy
                )

            # Use thread pool executor to run async function safely
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, release_seat_task())
                success = future.result()

            response_time = (time.time() - start_time) * 1000

            if success:
                self.seat_released_signal.emit(seat_id)
                self.log_signal.emit(f"✅ Successfully released seat {seat_id} in {response_time:.1f}ms")
            else:
                self.log_signal.emit(f"❌ Failed to release seat {seat_id}")

            return success
                
        except Exception as e:
            self.log_signal.emit(f"❌ Error releasing seat {seat_id}: {str(e)}")
            logger.error(f"Error releasing seat {seat_id}: {str(e)}")
            return False
    
    def switch_seat_async(self, seat_id: str, old_token: str, new_token: str) -> bool:
        """
        Switch a seat from one token to another using the async helper function
        """
        try:
            start_time = time.time()
            
            # Run the async switch_seat_immediate function in a separate thread to avoid event loop conflicts
            import concurrent.futures

            async def switch_seat_task():
                return await switch_seat_immediate(
                    seat_number=seat_id,
                    event_key=self.event_key,
                    old_token=old_token,
                    new_token=new_token,
                    channel_keys=self.channel_keys,
                    proxy=self.proxy
                )

            # Use thread pool executor to run async function safely
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, switch_seat_task())
                success = future.result()

            response_time = (time.time() - start_time) * 1000

            if success:
                self.log_signal.emit(f"✅ Successfully switched seat {seat_id} in {response_time:.1f}ms")
            else:
                self.log_signal.emit(f"❌ Failed to switch seat {seat_id}")

            return success
                
        except Exception as e:
            self.log_signal.emit(f"❌ Error switching seat {seat_id}: {str(e)}")
            logger.error(f"Error switching seat {seat_id}: {str(e)}")
            return False
    
    def _update_performance_stats(self, success: bool, response_time_ms: float):
        """Update performance statistics"""
        self.performance_stats['holds_attempted'] += 1
        
        if success:
            self.performance_stats['holds_successful'] += 1
        
        # Track last 50 response times for average calculation
        self.performance_stats['last_response_times'].append(response_time_ms)
        if len(self.performance_stats['last_response_times']) > 50:
            self.performance_stats['last_response_times'].pop(0)
        
        # Calculate average
        if self.performance_stats['last_response_times']:
            self.performance_stats['average_response_time'] = sum(
                self.performance_stats['last_response_times']
            ) / len(self.performance_stats['last_response_times'])
        
        # Emit performance signal
        self.performance_signal.emit(self.performance_stats.copy())
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get current performance statistics"""
        return self.performance_stats.copy()
    
    def renew_token_if_needed(self):
        """Check if token needs renewal and renew if necessary"""
        if not self.current_token or (self.token_expire_time and time.time() > (self.token_expire_time - 60)):
            self.log_signal.emit("🔄 Renewing token...")
            self._initialize_token()
    
    def cleanup(self):
        """Clean up resources"""
        self.current_token = None
        self.token_expire_time = None
        self.log_signal.emit("🧹 SimpleHoldManager cleaned up")
