#!/usr/bin/env python3
"""
Test script to verify the auto hold fixes work properly
"""

import asyncio
import logging
import sys
import time

# Import warning suppression first
import suppress_warnings  # noqa: F401

from logger_setup import setup_logger
from main_window import MainWindow
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Setup logging
logger = setup_logger()
logger.setLevel(logging.INFO)

def test_auto_hold_fixes():
    """Test that the auto hold system works without errors"""
    print("🔧 Testing Auto Hold Fixes")
    
    # Create a minimal QApplication
    app = QApplication(sys.argv)
    
    # Create main window
    main_window = MainWindow()
    main_window.show()
    
    # Simulate HWID check passing (enable tabs)
    main_window.set_fields_enabled(True)
    main_window.held_seats_tab.setEnabled(True)
    main_window.ticket_type_tab.setEnabled(True)
    
    # Set event key
    event_key = "moc-tar-festival-final-night-ibrahim-al-haka<PERSON>-hams-fekri"
    main_window.event_key_edit.setText(event_key)
    
    print(f"📝 Event key set: {event_key}")
    print(f"✅ Tabs enabled")
    
    # Test loading event
    def check_after_load():
        print(f"🔍 Checking after event load...")
        print(f"📊 Tickets info count: {len(main_window.tickets_info)}")
        print(f"📋 Auto-hold tracking: {len(main_window.auto_held_seats)} pending, {main_window.pending_seats} count")
        
        # Enable auto hold
        print("🔄 Enabling auto hold...")
        main_window.ticket_type_tab.auto_hold_checkbox.setChecked(True)
        main_window.ticket_type_tab.hold_all_checkbox.setChecked(True)
        
        print(f"✅ Auto Hold enabled: {main_window.ticket_type_tab.auto_hold_checkbox.isChecked()}")
        print(f"✅ Hold All enabled: {main_window.ticket_type_tab.hold_all_checkbox.isChecked()}")
        
        # Test simple hold manager
        if hasattr(main_window, 'simple_hold_manager') and main_window.simple_hold_manager:
            print("🔄 Testing SimpleHoldManager...")
            try:
                # Test holding a seat (this should not cause "Event loop is closed" error)
                test_seat = "Silver B9-S-1"
                success = main_window.simple_hold_manager.hold_seat_async(test_seat)
                print(f"✅ SimpleHoldManager test completed (success: {success})")
            except Exception as e:
                print(f"❌ SimpleHoldManager error: {e}")
        else:
            print("⚠️ SimpleHoldManager not available")
        
        # Wait a bit and check for zombie cleanup (should not happen)
        def final_check():
            print("🔍 Final check - no zombie cleanup should occur...")
            print(f"📊 Final auto-hold tracking: {len(main_window.auto_held_seats)} pending, {main_window.pending_seats} count")
            print("✅ Test completed successfully!")
            app.quit()
        
        QTimer.singleShot(5000, final_check)
    
    # Load event and check after 10 seconds
    main_window.on_load_event_info()
    QTimer.singleShot(10000, check_after_load)
    
    # Run the app
    app.exec_()

if __name__ == "__main__":
    test_auto_hold_fixes()
