#!/usr/bin/env python3
"""
Debug script to test channel keys and team information
"""

import asyncio
import logging
import json

# Import warning suppression first
import suppress_warnings  # noqa: F401

from logger_setup import setup_logger
from helper import hold_seat, build_channel_keys
from token_retrieval import get_hold_token, cache_event_id, get_cached_event_id
from webook_client import WebookClient

# Setup logging
logger = setup_logger()
logger.setLevel(logging.INFO)

async def test_channel_keys_debug():
    """Test channel keys and team information"""
    print("🔧 Testing Channel Keys Debug")
    
    # Test event
    event_key = "moc-tar-festival-final-night-ibrahim-al-hakami-hams-fekri"
    proxy = "p.webshare.io:80:ucegtvkm-rotate:mcg512cjss6l"
    
    print(f"📝 Event key: {event_key}")
    print(f"🌐 Proxy: {proxy}")
    
    # Step 1: Get event data to extract channel keys
    print("\n🔄 Step 1: Getting event data...")
    def get_event_data():
        client = WebookClient(proxy=proxy)
        try:
            return client.get_event_info(event_key=event_key, is_season=False)
        finally:
            client.close()
    
    event_data = await asyncio.get_event_loop().run_in_executor(None, get_event_data)
    if not event_data:
        print("❌ Failed to get event data")
        return
    
    cache_event_id(event_data)
    cached_event_id = get_cached_event_id()
    print(f"✅ Event ID cached: {cached_event_id}")
    
    # Step 2: Extract channel keys and team information
    print("\n🔄 Step 2: Extracting channel keys...")
    channel_keys = event_data['data']['channel_keys']
    print(f"📋 Raw channel keys: {json.dumps(channel_keys, indent=2)}")
    
    # Step 3: Test different team scenarios
    print("\n🔄 Step 3: Testing channel key building...")
    
    # Test with no team
    built_keys_no_team = build_channel_keys(channel_keys, None)
    print(f"🔑 Built keys (no team): {built_keys_no_team}")
    
    # Test with different teams (if available)
    if isinstance(channel_keys, dict):
        for team_id in channel_keys.keys():
            if team_id != 'common':
                built_keys_with_team = build_channel_keys(channel_keys, team_id)
                print(f"🔑 Built keys (team {team_id}): {built_keys_with_team}")
    
    # Step 4: Get a hold token
    print("\n🔄 Step 4: Getting hold token...")
    hold_token = await get_hold_token(cached_event_id, proxy=proxy)
    if not hold_token:
        print("❌ Failed to get hold token")
        return
    
    print(f"✅ Got hold token: {hold_token[:8]}...")
    
    # Step 5: Test holding with proper channel keys
    print("\n🔄 Step 5: Testing seat hold with proper channel keys...")
    
    # Get a free seat
    from helper import get_object_statuses, group_tickets_by_type_and_status
    seats = await get_object_statuses(event_key, "f636fc07-aefa-42e4-a939-179f4933be0b", proxy=proxy)
    grouped = group_tickets_by_type_and_status(seats, free_only=False)
    
    free_seat = None
    for ticket_type, statuses in grouped.items():
        if 'free' in statuses and statuses['free']:
            free_seat = list(statuses['free'].keys())[0]
            break
    
    if not free_seat:
        print("❌ No free seats found for testing")
        return
    
    print(f"✅ Testing with free seat: {free_seat}")
    
    # Test with no team
    print(f"\n🔄 Testing hold with no team...")
    try:
        success = await hold_seat(
            seat_number=free_seat,
            event_key=event_key,
            hold_token=hold_token,
            channel_keys=channel_keys,
            team_id=None,
            proxy=proxy
        )
        
        if success:
            print(f"✅ Successfully held seat {free_seat} with no team")
        else:
            print(f"❌ Failed to hold seat {free_seat} with no team")
            
    except Exception as e:
        print(f"❌ Exception holding seat {free_seat} with no team: {e}")
    
    # Test with first available team
    if isinstance(channel_keys, dict):
        for team_id in channel_keys.keys():
            if team_id != 'common':
                print(f"\n🔄 Testing hold with team {team_id}...")
                try:
                    success = await hold_seat(
                        seat_number=free_seat,
                        event_key=event_key,
                        hold_token=hold_token,
                        channel_keys=channel_keys,
                        team_id=team_id,
                        proxy=proxy
                    )
                    
                    if success:
                        print(f"✅ Successfully held seat {free_seat} with team {team_id}")
                        break
                    else:
                        print(f"❌ Failed to hold seat {free_seat} with team {team_id}")
                        
                except Exception as e:
                    print(f"❌ Exception holding seat {free_seat} with team {team_id}: {e}")
    
    print("\n🎉 Channel keys debug test completed!")

if __name__ == "__main__":
    asyncio.run(test_channel_keys_debug())
