#!/usr/bin/env python3
"""
Get detailed error information from seat holding attempts
"""

import asyncio
import logging
import json
import httpx

# Import warning suppression first
import suppress_warnings  # noqa: F401

from logger_setup import setup_logger
from helper import build_channel_keys, get_object_statuses, group_tickets_by_type_and_status
from token_retrieval import get_hold_token, cache_event_id, get_cached_event_id
from webook_client import WebookClient
from chart_token_manager import generate_x_signature as central_generate_x_signature

# Setup logging
logger = setup_logger()
logger.setLevel(logging.INFO)

async def test_detailed_error():
    """Get detailed error information"""
    print("🔧 Testing Detailed Error Information")
    
    # Test event
    event_key = "moc-tar-festival-final-night-ibrahim-al-hakami-hams-fekri"
    proxy = "p.webshare.io:80:ucegtvkm-rotate:mcg512cjss6l"
    
    # Get event data and cache event ID
    def get_event_data():
        client = WebookClient(proxy=proxy)
        try:
            return client.get_event_info(event_key=event_key, is_season=False)
        finally:
            client.close()
    
    event_data = await asyncio.get_event_loop().run_in_executor(None, get_event_data)
    cache_event_id(event_data)
    cached_event_id = get_cached_event_id()
    
    # Get a hold token
    hold_token = await get_hold_token(cached_event_id, proxy=proxy)
    print(f"✅ Got hold token: {hold_token[:8]}...")
    
    # Get channel keys
    channel_keys = event_data['data']['channel_keys']
    built_channel_keys = build_channel_keys(channel_keys, None)
    print(f"🔑 Built channel keys: {built_channel_keys}")
    
    # Get a free seat
    seats = await get_object_statuses(event_key, "f636fc07-aefa-42e4-a939-179f4933be0b", proxy=proxy)
    grouped = group_tickets_by_type_and_status(seats, free_only=False)
    
    free_seat = None
    for ticket_type, statuses in grouped.items():
        if 'free' in statuses and statuses['free']:
            free_seat = list(statuses['free'].keys())[0]
            break
    
    print(f"✅ Testing with free seat: {free_seat}")
    
    # Make detailed request to get full error information
    print("\n🔄 Making detailed request...")
    
    # Use IP address to bypass Cloudflare
    url = f'https://*************/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/hold-objects'

    json_data = {
        'events': [event_key],
        'holdToken': hold_token,
        'objects': [{'objectId': free_seat}],
        'channelKeys': built_channel_keys,
        'validateEventsLinkedToSameChart': True,
    }
    body_str = json.dumps(json_data, separators=(',', ':'))

    # Generate random browser ID
    import secrets
    browser_id = secrets.token_hex(8)

    headers = {
        'Host': 'cdn-eu.seatsio.net',
        'accept': '*/*',
        'content-type': 'application/json',
        'origin': 'https://cdn-eu.seatsio.net',
        'x-client-tool': 'Renderer',
        'x-browser-id': browser_id,
        'x-signature': central_generate_x_signature(body_str)
    }
    
    print(f"📋 Request URL: {url}")
    print(f"📋 Request body: {body_str}")
    print(f"📋 Request headers: {json.dumps(headers, indent=2)}")
    
    # Create HTTP client
    client_kwargs = {'timeout': httpx.Timeout(10.0), 'verify': False}
    if proxy:
        proxy_parts = proxy.split(':')
        if len(proxy_parts) == 4:
            proxy_url = f"http://{proxy_parts[2]}:{proxy_parts[3]}@{proxy_parts[0]}:{proxy_parts[1]}"
            client_kwargs['proxy'] = proxy_url
    
    async with httpx.AsyncClient(**client_kwargs) as client:
        response = await client.post(url, content=body_str, headers=headers)
        
        print(f"\n📊 Response status: {response.status_code}")
        print(f"📊 Response headers: {dict(response.headers)}")
        print(f"📊 Response text: {response.text}")
        
        # Try to parse response as JSON for more details
        try:
            response_json = response.json()
            print(f"📊 Response JSON: {json.dumps(response_json, indent=2)}")
        except:
            print("📊 Response is not valid JSON")
    
    # Try a different seat to see if it's seat-specific
    print("\n🔄 Trying with a different seat...")
    
    # Find another free seat
    other_free_seat = None
    for ticket_type, statuses in grouped.items():
        if 'free' in statuses and statuses['free']:
            for seat_id in statuses['free'].keys():
                if seat_id != free_seat:
                    other_free_seat = seat_id
                    break
            if other_free_seat:
                break
    
    if other_free_seat:
        print(f"✅ Testing with different seat: {other_free_seat}")
        
        json_data['objects'] = [{'objectId': other_free_seat}]
        body_str = json.dumps(json_data, separators=(',', ':'))
        headers['x-signature'] = central_generate_x_signature(body_str)
        
        async with httpx.AsyncClient(**client_kwargs) as client:
            response = await client.post(url, content=body_str, headers=headers)
            
            print(f"📊 Response status: {response.status_code}")
            print(f"📊 Response text: {response.text}")
    
    # Try without validateEventsLinkedToSameChart
    print("\n🔄 Trying without validateEventsLinkedToSameChart...")
    
    json_data_no_validate = {
        'events': [event_key],
        'holdToken': hold_token,
        'objects': [{'objectId': free_seat}],
        'channelKeys': built_channel_keys
    }
    body_str_no_validate = json.dumps(json_data_no_validate, separators=(',', ':'))
    headers_no_validate = headers.copy()
    headers_no_validate['x-signature'] = central_generate_x_signature(body_str_no_validate)
    
    async with httpx.AsyncClient(**client_kwargs) as client:
        response = await client.post(url, content=body_str_no_validate, headers=headers_no_validate)
        
        print(f"📊 Response status: {response.status_code}")
        print(f"📊 Response text: {response.text}")
    
    print("\n🎉 Detailed error test completed!")

if __name__ == "__main__":
    asyncio.run(test_detailed_error())
