#!/usr/bin/env python3
"""
Test script to debug event loading in main window
"""

import asyncio
import logging
import sys

# Import warning suppression first
import suppress_warnings  # noqa: F401

from logger_setup import setup_logger
from main_window import MainWindow
from PyQt5.QtWidgets import QApplication

# Setup logging
logger = setup_logger()
logger.setLevel(logging.INFO)

async def test_event_loading():
    """Test the main window's event loading process"""
    print("🔧 Testing main window event loading")
    
    # Create a minimal QApplication
    app = QApplication(sys.argv)
    
    # Create main window
    main_window = MainWindow()
    
    # Test event loading
    event_key = "moc-tar-festival-final-night-ibrahim-al-hakami-hams-fekri"
    is_season = False
    
    print(f"📝 Event key: {event_key}")
    print(f"🔄 Testing _load_event_info_async...")
    
    try:
        # Call the async loading method directly
        result = await main_window._load_event_info_async(event_key, is_season)
        
        if result:
            print(f"✅ Event loading successful")
            print(f"📊 Seats retrieved: {len(result['seats'])}")
            print(f"🎯 Event data keys: {list(result['webook_data']['data'].keys())}")
            
            # Test finalization
            print("🔄 Testing _finalize_event_loading...")
            main_window._finalize_event_loading(result)
            
            print(f"✅ Finalization successful")
            print(f"📋 Ticket types: {len(main_window.tickets_info)}")
            
            # Show ticket type summary
            for ticket_type, statuses in main_window.tickets_info.items():
                total_seats = sum(len(seats_dict) for seats_dict in statuses.values())
                print(f"  {ticket_type}: {total_seats} seats")
                
        else:
            print("❌ Event loading failed")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    app.quit()

if __name__ == "__main__":
    asyncio.run(test_event_loading())
