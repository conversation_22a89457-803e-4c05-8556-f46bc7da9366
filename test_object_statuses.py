#!/usr/bin/env python3
"""
Test script to debug get_object_statuses function
"""

import asyncio
import logging

# Import warning suppression first
import suppress_warnings  # noqa: F401

from logger_setup import setup_logger
from helper import get_object_statuses, group_tickets_by_type_and_status

# Setup logging
logger = setup_logger()
logger.setLevel(logging.INFO)

async def test_object_statuses():
    """Test the get_object_statuses function"""
    print("🔧 Testing get_object_statuses function")
    
    # Test data from the event
    event_key = "moc-tar-festival-final-night-ibrahim-al-hakami-hams-fekri"
    chart_key = "f636fc07-aefa-42e4-a939-179f4933be0b"
    proxy = "p.webshare.io:80:ucegtvkm-rotate:mcg512cjss6l"
    
    print(f"📝 Event key: {event_key}")
    print(f"🗺️ Chart key: {chart_key}")
    print(f"🌐 Proxy: {proxy}")
    
    try:
        print("🔄 Calling get_object_statuses...")
        seats = await get_object_statuses(event_key, chart_key, proxy=proxy)
        
        print(f"✅ Retrieved {len(seats)} seat objects")
        
        if seats:
            # Show first few seats as examples
            print("\n📋 Sample seats:")
            for i, seat in enumerate(seats[:5]):
                seat_id = seat.get('objectLabelOrUuid', 'N/A')
                status = seat.get('status', 'N/A')
                print(f"  {i+1}. {seat_id} - {status}")
            
            # Group by type and status
            print("\n🔄 Grouping seats by type and status...")
            grouped = group_tickets_by_type_and_status(seats, free_only=False)
            
            print(f"✅ Grouped into {len(grouped)} ticket types")
            
            # Show summary
            print("\n📊 Summary by ticket type:")
            for ticket_type, statuses in grouped.items():
                total_seats = sum(len(seats_dict) for seats_dict in statuses.values())
                status_summary = ", ".join([f"{status}: {len(seats_dict)}" for status, seats_dict in statuses.items()])
                print(f"  {ticket_type}: {total_seats} seats ({status_summary})")
        else:
            print("❌ No seats retrieved")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_object_statuses())
