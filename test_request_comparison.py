#!/usr/bin/env python3
"""
Compare the exact request format between helper.py and what should work
"""

import asyncio
import logging
import json

# Import warning suppression first
import suppress_warnings  # noqa: F401

from logger_setup import setup_logger
from helper import hold_seat, get_object_statuses, group_tickets_by_type_and_status
from token_retrieval import get_hold_token, cache_event_id, get_cached_event_id
from webook_client import WebookClient
from chart_token_manager import generate_x_signature as central_generate_x_signature

# Setup logging
logger = setup_logger()
logger.setLevel(logging.INFO)

async def test_request_comparison():
    """Compare request formats"""
    print("🔧 Testing Request Format Comparison")
    
    # Test event
    event_key = "moc-tar-festival-final-night-ibrahim-al-hakami-hams-fekri"
    proxy = "p.webshare.io:80:ucegtvkm-rotate:mcg512cjss6l"
    
    # Get event data and cache event ID
    def get_event_data():
        client = WebookClient(proxy=proxy)
        try:
            return client.get_event_info(event_key=event_key, is_season=False)
        finally:
            client.close()
    
    event_data = await asyncio.get_event_loop().run_in_executor(None, get_event_data)
    cache_event_id(event_data)
    cached_event_id = get_cached_event_id()
    
    # Get a hold token
    hold_token = await get_hold_token(cached_event_id, proxy=proxy)
    print(f"✅ Got hold token: {hold_token[:8]}...")
    
    # Get seat data to find a free seat
    seats = await get_object_statuses(event_key, "f636fc07-aefa-42e4-a939-179f4933be0b", proxy=proxy)
    grouped = group_tickets_by_type_and_status(seats, free_only=False)
    
    free_seat = None
    for ticket_type, statuses in grouped.items():
        if 'free' in statuses and statuses['free']:
            free_seat = list(statuses['free'].keys())[0]
            break
    
    print(f"✅ Testing with free seat: {free_seat}")
    
    # Test 1: Show what helper.py is sending
    print("\n📋 Helper.py request format:")
    json_data_helper = {
        'events': [event_key], 
        'holdToken': hold_token,
        'objects': [{'objectId': free_seat}], 
        'channelKeys': ['NO_CHANNEL'],
        'validateEventsLinkedToSameChart': True,
    }
    body_str_helper = json.dumps(json_data_helper, separators=(',', ':'))
    print(f"URL: https://*************/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/hold-objects")
    print(f"Body: {body_str_helper}")
    print(f"X-Signature: {central_generate_x_signature(body_str_helper)}")
    
    # Test 2: Try a different format that might work
    print("\n📋 Alternative request format (without validateEventsLinkedToSameChart):")
    json_data_alt = {
        'events': [event_key], 
        'holdToken': hold_token,
        'objects': [{'objectId': free_seat}], 
        'channelKeys': ['NO_CHANNEL']
    }
    body_str_alt = json.dumps(json_data_alt, separators=(',', ':'))
    print(f"Body: {body_str_alt}")
    print(f"X-Signature: {central_generate_x_signature(body_str_alt)}")
    
    # Test 3: Try with different channel keys
    print("\n📋 Alternative request format (with different channel keys):")
    json_data_alt2 = {
        'events': [event_key], 
        'holdToken': hold_token,
        'objects': [{'objectId': free_seat}], 
        'channelKeys': []
    }
    body_str_alt2 = json.dumps(json_data_alt2, separators=(',', ':'))
    print(f"Body: {body_str_alt2}")
    print(f"X-Signature: {central_generate_x_signature(body_str_alt2)}")
    
    # Test 4: Actually test these formats
    import httpx
    
    print("\n🔄 Testing alternative format 1 (without validateEventsLinkedToSameChart)...")
    try:
        headers = {
            'Host': 'cdn-eu.seatsio.net', 
            'accept': '*/*',
            'content-type': 'application/json', 
            'origin': 'https://cdn-eu.seatsio.net',
            'x-signature': central_generate_x_signature(body_str_alt)
        }
        
        client_kwargs = {'timeout': httpx.Timeout(10.0), 'verify': False}
        if proxy:
            proxy_parts = proxy.split(':')
            if len(proxy_parts) == 4:
                proxy_url = f"http://{proxy_parts[2]}:{proxy_parts[3]}@{proxy_parts[0]}:{proxy_parts[1]}"
                client_kwargs['proxy'] = proxy_url
        
        async with httpx.AsyncClient(**client_kwargs) as client:
            response = await client.post(
                'https://*************/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/hold-objects',
                content=body_str_alt, 
                headers=headers
            )
            
            print(f"📊 Status: {response.status_code}")
            print(f"📊 Response: {response.text[:200]}")
            
            if response.status_code == 204:
                print("✅ Alternative format 1 worked!")
            else:
                print("❌ Alternative format 1 failed")
                
    except Exception as e:
        print(f"❌ Exception with alternative format 1: {e}")
    
    print("\n🔄 Testing alternative format 2 (empty channel keys)...")
    try:
        headers = {
            'Host': 'cdn-eu.seatsio.net', 
            'accept': '*/*',
            'content-type': 'application/json', 
            'origin': 'https://cdn-eu.seatsio.net',
            'x-signature': central_generate_x_signature(body_str_alt2)
        }
        
        async with httpx.AsyncClient(**client_kwargs) as client:
            response = await client.post(
                'https://*************/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/hold-objects',
                content=body_str_alt2, 
                headers=headers
            )
            
            print(f"📊 Status: {response.status_code}")
            print(f"📊 Response: {response.text[:200]}")
            
            if response.status_code == 204:
                print("✅ Alternative format 2 worked!")
            else:
                print("❌ Alternative format 2 failed")
                
    except Exception as e:
        print(f"❌ Exception with alternative format 2: {e}")

if __name__ == "__main__":
    asyncio.run(test_request_comparison())
