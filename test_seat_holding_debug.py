#!/usr/bin/env python3
"""
Debug script to test seat holding functionality
"""

import asyncio
import logging
import json

# Import warning suppression first
import suppress_warnings  # noqa: F401

from logger_setup import setup_logger
from helper import hold_seat, get_object_statuses, group_tickets_by_type_and_status
from token_retrieval import get_hold_token, cache_event_id, get_cached_event_id
from webook_client import WebookClient

# Setup logging
logger = setup_logger()
logger.setLevel(logging.INFO)

async def test_seat_holding():
    """Test the seat holding functionality step by step"""
    print("🔧 Testing Seat Holding Debug")
    
    # Test event
    event_key = "moc-tar-festival-final-night-ibrahim-al-hakami-hams-fekri"
    proxy = "p.webshare.io:80:ucegtvkm-rotate:mcg512cjss6l"
    
    print(f"📝 Event key: {event_key}")
    print(f"🌐 Proxy: {proxy}")
    
    # Step 1: Get event data and cache event ID
    print("\n🔄 Step 1: Getting event data...")
    def get_event_data():
        client = WebookClient(proxy=proxy)
        try:
            return client.get_event_info(event_key=event_key, is_season=False)
        finally:
            client.close()
    
    event_data = await asyncio.get_event_loop().run_in_executor(None, get_event_data)
    if not event_data:
        print("❌ Failed to get event data")
        return
    
    cache_event_id(event_data)
    cached_event_id = get_cached_event_id()
    print(f"✅ Event ID cached: {cached_event_id}")
    
    # Step 2: Get seat data to find available seats
    print("\n🔄 Step 2: Getting seat data...")
    seats = await get_object_statuses(event_key, "f636fc07-aefa-42e4-a939-179f4933be0b", proxy=proxy)
    if not seats:
        print("❌ Failed to get seat data")
        return
    
    print(f"✅ Retrieved {len(seats)} seats")
    
    # Group seats and find free ones
    grouped = group_tickets_by_type_and_status(seats, free_only=False)
    
    # Find a free seat to test with
    free_seat = None
    for ticket_type, statuses in grouped.items():
        if 'free' in statuses and statuses['free']:
            free_seat = list(statuses['free'].keys())[0]
            break
    
    if not free_seat:
        print("❌ No free seats found for testing")
        return
    
    print(f"✅ Found free seat for testing: {free_seat}")
    
    # Step 3: Get a hold token
    print("\n🔄 Step 3: Getting hold token...")
    hold_token = await get_hold_token(cached_event_id, proxy=proxy)
    if not hold_token:
        print("❌ Failed to get hold token")
        return
    
    print(f"✅ Got hold token: {hold_token[:8]}...")
    
    # Step 4: Test holding the seat
    print(f"\n🔄 Step 4: Testing seat hold for {free_seat}...")
    try:
        success = await hold_seat(
            seat_number=free_seat,
            event_key=event_key,
            hold_token=hold_token,
            channel_keys=['NO_CHANNEL'],
            proxy=proxy
        )
        
        if success:
            print(f"✅ Successfully held seat {free_seat}")
        else:
            print(f"❌ Failed to hold seat {free_seat}")
            
    except Exception as e:
        print(f"❌ Exception holding seat {free_seat}: {e}")
        import traceback
        traceback.print_exc()
    
    # Step 5: Test with different parameters
    print(f"\n🔄 Step 5: Testing with different channel keys...")
    try:
        # Try with different channel keys
        success = await hold_seat(
            seat_number=free_seat,
            event_key=event_key,
            hold_token=hold_token,
            channel_keys=None,  # Let it default
            proxy=proxy
        )
        
        if success:
            print(f"✅ Successfully held seat {free_seat} with default channel keys")
        else:
            print(f"❌ Failed to hold seat {free_seat} with default channel keys")
            
    except Exception as e:
        print(f"❌ Exception holding seat {free_seat} with default channel keys: {e}")
    
    # Step 6: Test the isolated implementation from SimpleHoldManager
    print(f"\n🔄 Step 6: Testing isolated implementation...")
    try:
        import httpx
        from chart_token_manager import generate_x_signature as central_generate_x_signature
        
        url = f'https://**************/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/hold-objects'
        
        json_data = {
            'events': [event_key],
            'holdToken': hold_token,
            'objects': [{'objectId': free_seat}],
            'channelKeys': ['NO_CHANNEL'],
            'validateEventsLinkedToSameChart': True,
        }
        body_str = json.dumps(json_data, separators=(',', ':'))
        
        headers = {
            'Host': 'cdn-eu.seatsio.net',
            'accept': '*/*',
            'content-type': 'application/json',
            'origin': 'https://cdn-eu.seatsio.net',
            'x-signature': central_generate_x_signature(body_str)
        }
        
        # Create isolated HTTP client
        client_kwargs = {
            'timeout': httpx.Timeout(10.0),
            'verify': False
        }
        if proxy:
            proxy_parts = proxy.split(':')
            if len(proxy_parts) == 4:
                proxy_url = f"http://{proxy_parts[2]}:{proxy_parts[3]}@{proxy_parts[0]}:{proxy_parts[1]}"
                client_kwargs['proxy'] = proxy_url
        
        async with httpx.AsyncClient(**client_kwargs) as client:
            response = await client.post(url, content=body_str, headers=headers)
            
            print(f"📊 Response status: {response.status_code}")
            print(f"📊 Response headers: {dict(response.headers)}")
            print(f"📊 Response text: {response.text[:200]}")
            
            if response.status_code == 204:
                print(f"✅ Isolated implementation successfully held seat {free_seat}")
            else:
                print(f"❌ Isolated implementation failed to hold seat {free_seat}")
                
    except Exception as e:
        print(f"❌ Exception in isolated implementation: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n🎉 Seat holding debug test completed!")

if __name__ == "__main__":
    asyncio.run(test_seat_holding())
