#!/usr/bin/env python3
"""
Test script to debug ticket type tab population
"""

import asyncio
import logging
import sys

# Import warning suppression first
import suppress_warnings  # noqa: F401

from logger_setup import setup_logger
from main_window import MainWindow
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Setup logging
logger = setup_logger()
logger.setLevel(logging.INFO)

def test_ticket_type_tab():
    """Test the ticket type tab population"""
    print("🔧 Testing ticket type tab population")
    
    # Create a minimal QApplication
    app = QApplication(sys.argv)
    
    # Create main window
    main_window = MainWindow()
    main_window.show()
    
    # Simulate HWID check passing (enable tabs)
    main_window.set_fields_enabled(True)
    main_window.held_seats_tab.setEnabled(True)
    main_window.ticket_type_tab.setEnabled(True)
    
    print(f"✅ Tabs enabled")
    print(f"📋 Ticket Type Tab enabled: {main_window.ticket_type_tab.isEnabled()}")
    print(f"📋 Held Seats Tab enabled: {main_window.held_seats_tab.isEnabled()}")
    
    # Set event key
    event_key = "moc-tar-festival-final-night-ibrahim-al-hakami-hams-fekri"
    main_window.event_key_edit.setText(event_key)
    
    print(f"📝 Event key set: {event_key}")
    
    # Test loading event
    def check_after_load():
        print(f"🔍 Checking ticket type tab after load...")
        print(f"📊 Tickets info count: {len(main_window.tickets_info)}")
        print(f"📋 Tab enabled: {main_window.ticket_type_tab.isEnabled()}")
        print(f"📋 Tab tickets info count: {len(main_window.ticket_type_tab.tickets_info)}")
        print(f"📋 Table row count: {main_window.ticket_type_tab.table.rowCount()}")

        # If tickets_info is empty, try to manually trigger finalization
        if len(main_window.tickets_info) == 0:
            print("🔧 Tickets info is empty, trying manual finalization...")
            try:
                # Try to get the result from the async loading
                event_key = "moc-tar-festival-final-night-ibrahim-al-hakami-hams-fekri"
                result = main_window.run_async(main_window._load_event_info_async(event_key, False))
                if result:
                    print(f"📊 Manual async result: {len(result['seats'])} seats")
                    main_window._finalize_event_loading(result)
                    print(f"📊 After manual finalization: {len(main_window.tickets_info)} ticket types")
                else:
                    print("❌ Manual async loading failed")
            except Exception as e:
                print(f"❌ Manual finalization error: {e}")
                import traceback
                traceback.print_exc()

        # Check if auto hold checkboxes are working
        print(f"🔘 Auto Hold checkbox: {main_window.ticket_type_tab.auto_hold_checkbox.isChecked()}")
        print(f"🔘 Hold All checkbox: {main_window.ticket_type_tab.hold_all_checkbox.isChecked()}")
        print(f"🔘 Hold All enabled flag: {getattr(main_window.ticket_type_tab, 'hold_all_enabled', False)}")

        # Test enabling hold all
        print("🔄 Testing Hold All checkbox...")
        main_window.ticket_type_tab.hold_all_checkbox.setChecked(True)
        print(f"🔘 Hold All enabled after check: {getattr(main_window.ticket_type_tab, 'hold_all_enabled', False)}")

        # Test auto hold
        print("🔄 Testing Auto Hold checkbox...")
        main_window.ticket_type_tab.auto_hold_checkbox.setChecked(True)
        print(f"🔘 Auto Hold enabled after check: {getattr(main_window.ticket_type_tab, 'auto_hold_enabled', False)}")

        app.quit()
    
    # Load event and check after 10 seconds
    main_window.on_load_event_info()
    QTimer.singleShot(10000, check_after_load)
    
    # Run the app
    app.exec_()

if __name__ == "__main__":
    test_ticket_type_tab()
