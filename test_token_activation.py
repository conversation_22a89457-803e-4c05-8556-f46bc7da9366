#!/usr/bin/env python3
"""
Test token activation before using it for seat holding
"""

import asyncio
import logging

# Import warning suppression first
import suppress_warnings  # noqa: F401

from logger_setup import setup_logger
from helper import hold_seat, activate_hold_token
from token_retrieval import get_hold_token, cache_event_id, get_cached_event_id
from webook_client import WebookClient

# Setup logging
logger = setup_logger()
logger.setLevel(logging.INFO)

async def test_token_activation():
    """Test token activation before holding seats"""
    print("🔧 Testing Token Activation")
    
    # Test event
    event_key = "moc-tar-festival-final-night-ibrahim-al-hakami-hams-fekri"
    proxy = "p.webshare.io:80:ucegtvkm-rotate:mcg512cjss6l"
    
    print(f"📝 Event key: {event_key}")
    print(f"🌐 Proxy: {proxy}")
    
    # Step 1: Get event data and cache event ID
    print("\n🔄 Step 1: Getting event data...")
    def get_event_data():
        client = WebookClient(proxy=proxy)
        try:
            return client.get_event_info(event_key=event_key, is_season=False)
        finally:
            client.close()
    
    event_data = await asyncio.get_event_loop().run_in_executor(None, get_event_data)
    if not event_data:
        print("❌ Failed to get event data")
        return
    
    cache_event_id(event_data)
    cached_event_id = get_cached_event_id()
    print(f"✅ Event ID cached: {cached_event_id}")
    
    # Step 2: Get a hold token
    print("\n🔄 Step 2: Getting hold token...")
    hold_token = await get_hold_token(cached_event_id, proxy=proxy)
    if not hold_token:
        print("❌ Failed to get hold token")
        return
    
    print(f"✅ Got hold token: {hold_token[:8]}...")
    
    # Step 3: Activate the hold token
    print("\n🔄 Step 3: Activating hold token...")
    try:
        time_left = await activate_hold_token(hold_token, proxy=proxy)
        print(f"✅ Token activated successfully! Time left: {time_left} seconds")
    except Exception as e:
        print(f"❌ Failed to activate token: {e}")
        return
    
    # Step 4: Now try to hold a seat with the activated token
    print("\n🔄 Step 4: Testing seat hold with activated token...")
    
    # Get a free seat
    from helper import get_object_statuses, group_tickets_by_type_and_status
    seats = await get_object_statuses(event_key, "f636fc07-aefa-42e4-a939-179f4933be0b", proxy=proxy)
    grouped = group_tickets_by_type_and_status(seats, free_only=False)
    
    free_seat = None
    for ticket_type, statuses in grouped.items():
        if 'free' in statuses and statuses['free']:
            free_seat = list(statuses['free'].keys())[0]
            break
    
    if not free_seat:
        print("❌ No free seats found for testing")
        return
    
    print(f"✅ Testing with free seat: {free_seat}")
    
    # Test holding with activated token
    try:
        success = await hold_seat(
            seat_number=free_seat,
            event_key=event_key,
            hold_token=hold_token,
            channel_keys=event_data['data']['channel_keys'],
            team_id=None,
            proxy=proxy
        )
        
        if success:
            print(f"✅ Successfully held seat {free_seat} with activated token!")
        else:
            print(f"❌ Failed to hold seat {free_seat} even with activated token")
            
    except Exception as e:
        print(f"❌ Exception holding seat {free_seat} with activated token: {e}")
    
    print("\n🎉 Token activation test completed!")

if __name__ == "__main__":
    asyncio.run(test_token_activation())
