#!/usr/bin/env python3
"""
Test script to verify the fixed token retrieval using WebookClient
"""

import asyncio
import logging

# Import warning suppression first
import suppress_warnings  # noqa: F401

from logger_setup import setup_logger
from token_retrieval import get_hold_token, create_hold_tokens_batch, cache_event_id, get_cached_event_id
from webook_client import WebookClient

# Setup logging
logger = setup_logger()
logger.setLevel(logging.INFO)

async def test_token_retrieval():
    """Test the updated token retrieval system"""
    print("🔧 Testing Updated Token Retrieval System")
    
    # Test event
    event_key = "moc-tar-festival-final-night-ibrahim-al-hakami-hams-fekri"
    proxy = "p.webshare.io:80:ucegtvkm-rotate:mcg512cjss6l"
    
    print(f"📝 Event key: {event_key}")
    print(f"🌐 Proxy: {proxy}")
    
    # Step 1: Get event data to cache event ID
    print("\n🔄 Step 1: Getting event data to cache event ID...")
    def get_event_data():
        client = WebookClient(proxy=proxy)
        try:
            return client.get_event_info(event_key=event_key, is_season=False)
        finally:
            client.close()
    
    event_data = await asyncio.get_event_loop().run_in_executor(None, get_event_data)
    if not event_data:
        print("❌ Failed to get event data")
        return
    
    # Cache the event ID
    cache_event_id(event_data)
    cached_event_id = get_cached_event_id()
    print(f"✅ Event ID cached: {cached_event_id}")
    
    # Step 2: Test single token retrieval
    print("\n🔄 Step 2: Testing single token retrieval...")
    try:
        hold_token = await get_hold_token(cached_event_id, proxy=proxy)
        if hold_token:
            print(f"✅ Single token retrieved: {hold_token[:8]}...")
        else:
            print("❌ Failed to retrieve single token")
            return
    except Exception as e:
        print(f"❌ Error retrieving single token: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # Step 3: Test batch token retrieval
    print("\n🔄 Step 3: Testing batch token retrieval (3 tokens)...")
    try:
        batch_tokens = await create_hold_tokens_batch(3, cached_event_id, proxy=proxy)
        if batch_tokens:
            print(f"✅ Batch tokens retrieved: {len(batch_tokens)} tokens")
            for i, token in enumerate(batch_tokens, 1):
                print(f"  {i}. {token[:8]}...")
        else:
            print("❌ Failed to retrieve batch tokens")
    except Exception as e:
        print(f"❌ Error retrieving batch tokens: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n🎉 Token retrieval test completed!")

if __name__ == "__main__":
    asyncio.run(test_token_retrieval())
