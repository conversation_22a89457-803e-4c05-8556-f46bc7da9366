"""
Quick test script to verify the ultra-fast hold manager fixes
"""

import asyncio
import logging
import time
from ultra_fast_hold_manager import UltraFastHoldManager

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_ultra_fast_manager():
    """Test the fixed ultra-fast hold manager"""
    
    # Test configuration (use your actual event data)
    event_key = "moc-tar-festival-final-night-i<PERSON><PERSON>-<PERSON>-ha<PERSON><PERSON>-ha<PERSON>-fek<PERSON>"  # From the logs
    chart_key = "your_chart_key"  # Replace with actual chart key
    channel_keys = ["NO_CHANNEL"]
    team_id = "default"
    proxy = None  # Add your proxy if needed
    event_id = None  # Add your event ID if available
    
    logger.info("🚀 Testing Ultra-Fast Hold Manager fixes...")
    
    # Create the manager
    manager = UltraFastHoldManager(
        event_key=event_key,
        chart_key=chart_key,
        channel_keys=channel_keys,
        team_id=team_id,
        proxy=proxy,
        event_id=event_id
    )
    
    # Wait for token initialization
    await asyncio.sleep(2.0)
    
    # Test with a realistic seat ID from the logs
    test_seat = "Silver B8-P-16"
    
    logger.info(f"🎯 Testing hold operation for seat: {test_seat}")
    
    # Test queue time (should be <100ms)
    start_time = time.time()
    success = manager.hold_seat_ultra_fast(test_seat)
    queue_time = (time.time() - start_time) * 1000.0
    
    logger.info(f"📊 Queue operation result: {success}")
    logger.info(f"⚡ Queue time: {queue_time:.1f}ms")
    
    if queue_time < 100.0:
        logger.info("✅ Queue time target met (<100ms)")
    else:
        logger.warning(f"⚠️ Queue time target missed ({queue_time:.1f}ms)")
    
    # Wait a bit for the async operation to complete
    await asyncio.sleep(2.0)
    
    # Get performance stats
    stats = manager.get_performance_stats()
    logger.info(f"📈 Performance stats: {stats}")
    
    # Cleanup
    manager.cleanup()
    
    logger.info("✅ Test completed!")

if __name__ == "__main__":
    asyncio.run(test_ultra_fast_manager())
