# token_retrieval.py - High-performance asynchronous hold token retrieval

import asyncio
import logging
import threading
from typing import Optional, Dict, Any, List

# Import the new asynchronous account token manager functions
from account_token_manager import get_account_token, report_invalid_token
# Import WebookClient for Cloudflare-safe requests
from webook_client import WebookClient

logger = logging.getLogger('webook_pro')

# --- Event ID Caching ---
# This remains thread-safe for simplicity, as it might be accessed from different contexts.
_event_id_cache: Dict[str, Any] = {}
_event_id_lock = threading.Lock()

def cache_event_id(event_data: Dict[str, Any]):
    """
    Extracts and caches the event ID from the event data response.
    """
    if not event_data or not isinstance(event_data, dict):
        return
    
    event_id = event_data.get("data", {}).get("_id")
    if event_id:
        with _event_id_lock:
            _event_id_cache["default"] = event_id
            logger.info(f"Cached new event ID: {event_id}")

def get_cached_event_id() -> Optional[str]:
    """Gets the most recently cached event ID."""
    with _event_id_lock:
        return _event_id_cache.get("default")

# --- Asynchronous Hold Token Retrieval ---

async def get_hold_token(
    event_id: str,
    proxy: Optional[str] = None,
    client: Optional[Any] = None,
    _recursion_count: int = 0
) -> Optional[str]:
    """
    Asynchronously gets a single hold token from the Webook API using WebookClient
    to avoid Cloudflare 403 errors.

    Args:
        event_id: The event ID to get a hold token for.
        proxy: The proxy to use for the request.
        client: An optional existing httpx.AsyncClient to use (ignored, kept for compatibility).
        _recursion_count: Internal parameter to prevent infinite recursion.

    Returns:
        A hold token string, or None if it failed.
    """
    # Prevent infinite recursion
    if _recursion_count > 5:
        logger.error("Maximum recursion depth reached in get_hold_token")
        return None

    # Get an account token from our high-speed manager
    access_token = get_account_token()
    if not access_token:
        logger.error("Failed to get an account token from the manager.")
        return None

    try:
        # Use WebookClient to make Cloudflare-safe requests
        def get_token_sync():
            webook_client = WebookClient(proxy=proxy)
            try:
                return webook_client.get_hold_token(event_id, access_token)
            finally:
                webook_client.close()

        # Run the synchronous WebookClient call in a thread pool to keep it async
        hold_token = await asyncio.get_event_loop().run_in_executor(None, get_token_sync)

        if not hold_token:
            # Check if we should retry with a new token
            logger.warning(f"Failed to get hold token with token {access_token[:8]}... Reporting as invalid and retrying.")
            report_invalid_token(access_token)
            # Recursively try again with a new token
            return await get_hold_token(event_id, proxy, client, _recursion_count + 1)

        logger.info(f"Successfully got hold token: {hold_token[:8]}... using token {access_token[:8]}...")
        return hold_token

    except Exception as e:
        logger.error(f"Unexpected error in get_hold_token: {e}")
        return None


async def create_hold_tokens_batch(
    count: int,
    event_id: Optional[str] = None,
    proxy: Optional[str] = None
) -> List[str]:
    """
    Creates a batch of hold tokens concurrently using WebookClient
    to avoid Cloudflare 403 errors.

    Args:
        count: The number of hold tokens to create.
        event_id: The event ID to use. If None, uses the cached ID.
        proxy: The proxy to use for all requests.

    Returns:
        A list of successfully created hold token strings.
    """
    if not event_id:
        event_id = get_cached_event_id()
        if not event_id:
            logger.critical("Cannot create token batch: No Event ID is cached.")
            return []

    # Create a list of tasks to run in parallel
    # Each task will create its own WebookClient to avoid sharing issues
    tasks = [get_hold_token(event_id, proxy=proxy) for _ in range(count)]

    # Run all tasks concurrently and wait for them to complete
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # Filter out any failures (which will be None or Exceptions)
    successful_tokens = [token for token in results if isinstance(token, str)]

    logger.info(f"Successfully created {len(successful_tokens)} of {count} requested hold tokens.")

    return successful_tokens
