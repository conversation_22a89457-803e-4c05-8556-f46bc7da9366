"""
Ultra-Fast Hold Manager - Optimized for <100ms response times
Eliminates all performance bottlenecks with persistent connections and minimal overhead
"""

import asyncio
import logging
import time
import threading
import json
import secrets
import httpx
from typing import Optional, List, Dict, Any
from PyQt5.QtCore import QObject, pyqtSignal
from concurrent.futures import ThreadPoolExecutor
from collections import deque

from helper import build_channel_keys
from token_retrieval import get_hold_token, get_cached_event_id
from chart_token_manager import generate_x_signature

logger = logging.getLogger("webook_pro")

# Global persistent HTTP client and thread pool for maximum performance
_global_client = None
_global_executor = None
_client_lock = threading.Lock()

def get_global_client(proxy: Optional[str] = None) -> httpx.AsyncClient:
    """Get or create a global persistent HTTP client"""
    global _global_client
    with _client_lock:
        if _global_client is None or _global_client.is_closed:
            client_kwargs = {
                'timeout': httpx.Timeout(5.0),
                'verify': False,
                'limits': httpx.Limits(max_keepalive_connections=20, max_connections=50),
                'http2': True  # Enable HTTP/2 for better performance
            }
            if proxy:
                parts = proxy.split(':')
                if len(parts) == 4:
                    client_kwargs['proxy'] = f"http://{parts[2]}:{parts[3]}@{parts[0]}:{parts[1]}"
            
            _global_client = httpx.AsyncClient(**client_kwargs)
        return _global_client

def get_global_executor() -> ThreadPoolExecutor:
    """Get or create a global thread pool executor"""
    global _global_executor
    if _global_executor is None:
        _global_executor = ThreadPoolExecutor(
            max_workers=10,
            thread_name_prefix="UltraFastHold"
        )
    return _global_executor


class UltraFastHoldManager(QObject):
    """
    Ultra-Fast Hold Manager optimized for <100ms response times
    Uses persistent connections, pre-built requests, and minimal overhead
    """
    
    # Signals
    seat_held_signal = pyqtSignal(str, str)  # seat_id, token
    seat_released_signal = pyqtSignal(str)   # seat_id
    log_signal = pyqtSignal(str)
    performance_signal = pyqtSignal(dict)
    
    def __init__(self, event_key: str, chart_key: str, channel_keys: List[str], 
                 team_id: str, proxy: Optional[str] = None, event_id: Optional[str] = None):
        super().__init__()
        
        self.event_key = event_key
        self.chart_key = chart_key
        self.channel_keys = channel_keys or ['NO_CHANNEL']
        self.team_id = team_id
        self.proxy = proxy
        self.event_id = event_id
        
        # Current hold token
        self.current_token = None
        self.token_expire_time = None
        
        # Performance tracking
        self.performance_stats = {
            'holds_attempted': 0,
            'holds_successful': 0,
            'average_response_time': 0.0,
            'last_response_times': deque(maxlen=50)
        }
        
        # Ultra-fast optimizations
        self._setup_optimizations()
        
        # Initialize with a token
        self._initialize_token()
    
    def _setup_optimizations(self):
        """Setup all performance optimizations"""
        # Pre-build channel keys to avoid repeated computation
        self.built_channel_keys = build_channel_keys(self.channel_keys, self.team_id)
        
        # Pre-build base request template
        self.base_request = {
            'events': [self.event_key],
            'holdToken': None,  # Will be filled in
            'objects': [{'objectId': None}],  # Will be filled in
            'channelKeys': self.built_channel_keys,
            'validateEventsLinkedToSameChart': True,
        }
        
        # Pre-build headers template
        self.base_headers = {
            'Host': 'cdn-eu.seatsio.net',
            'accept': '*/*',
            'content-type': 'application/json',
            'origin': 'https://cdn-eu.seatsio.net',
            'x-client-tool': 'Renderer',
        }
        
        # Pre-generate browser IDs for rotation
        self.browser_ids = [secrets.token_hex(8) for _ in range(10)]
        self.browser_id_index = 0
        
        # Seatsio API endpoint
        self.hold_url = 'https://*************/system/public/3d443a0c-83b8-4a11-8c57-3db9d116ef76/events/groups/actions/hold-objects'
    
    def _initialize_token(self):
        """Get an initial hold token"""
        try:
            # Use cached event ID if available
            event_id = self.event_id or get_cached_event_id()
            
            # Get token asynchronously to avoid blocking
            def get_token_async():
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        token = loop.run_until_complete(
                            get_hold_token(event_id, proxy=self.proxy)
                        )
                        if token:
                            self.current_token = token
                            # Assume 15 minutes expiry if not specified
                            self.token_expire_time = time.time() + (15 * 60)
                            self.log_signal.emit(f"✅ Initialized with token: {token[:8]}...")
                        else:
                            self.log_signal.emit("❌ Failed to get initial token")
                    finally:
                        loop.close()
                except Exception as e:
                    self.log_signal.emit(f"❌ Error getting initial token: {str(e)}")
                    logger.error(f"Error getting initial token: {str(e)}")
            
            # Run in background thread
            threading.Thread(target=get_token_async, daemon=True).start()
            
        except Exception as e:
            self.log_signal.emit(f"❌ Error initializing token: {str(e)}")
            logger.error(f"Error initializing token: {str(e)}")
    
    def _ensure_valid_token(self) -> bool:
        """Ensure we have a valid token"""
        if not self.current_token:
            return False
        
        # Check if token is about to expire (within 2 minutes)
        if self.token_expire_time and (time.time() + 120) >= self.token_expire_time:
            # Token is expiring soon, refresh it
            self._initialize_token()
            return bool(self.current_token)
        
        return True
    
    def _get_next_browser_id(self) -> str:
        """Get next browser ID from pre-generated pool"""
        browser_id = self.browser_ids[self.browser_id_index]
        self.browser_id_index = (self.browser_id_index + 1) % len(self.browser_ids)
        return browser_id
    
    def hold_seat_ultra_fast(self, seat_id: str) -> bool:
        """
        Ultra-fast seat holding with <100ms target
        Returns immediately, actual hold happens asynchronously
        """
        if not self._ensure_valid_token():
            self.log_signal.emit(f"❌ No valid token available to hold seat {seat_id}")
            return False
        
        try:
            start_time = time.time()
            
            # Pre-build request with current data
            request_data = self.base_request.copy()
            request_data['holdToken'] = self.current_token
            request_data['objects'] = [{'objectId': seat_id}]
            
            # Pre-build headers
            headers = self.base_headers.copy()
            headers['x-browser-id'] = self._get_next_browser_id()
            
            # Serialize request body once
            body_str = json.dumps(request_data, separators=(',', ':'))
            headers['x-signature'] = generate_x_signature(body_str)
            
            # Submit to global thread pool for immediate return
            executor = get_global_executor()
            executor.submit(self._execute_hold, seat_id, body_str, headers, start_time)
            
            return True
            
        except Exception as e:
            self.log_signal.emit(f"❌ Error queuing hold for seat {seat_id}: {str(e)}")
            logger.error(f"Error queuing hold for seat {seat_id}: {str(e)}")
            return False
    
    def _execute_hold(self, seat_id: str, body_str: str, headers: Dict[str, str], start_time: float):
        """Execute the actual hold operation in background thread"""
        try:
            # Create isolated event loop for this operation
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                success, response_time = loop.run_until_complete(
                    self._async_hold_seat(seat_id, body_str, headers, start_time)
                )
                
                # Update performance stats
                self._update_performance_stats(success, response_time)
                
                if success:
                    self.seat_held_signal.emit(seat_id, self.current_token)
                    self.log_signal.emit(f"✅ Held seat {seat_id} in {response_time:.1f}ms")
                else:
                    self.log_signal.emit(f"❌ Failed to hold seat {seat_id}")
                    
            finally:
                loop.close()
                
        except Exception as e:
            self.log_signal.emit(f"❌ Error executing hold for seat {seat_id}: {str(e)}")
            logger.error(f"Error executing hold for seat {seat_id}: {str(e)}")
    
    async def _async_hold_seat(self, seat_id: str, body_str: str, headers: Dict[str, str], start_time: float) -> tuple[bool, float]:
        """Async hold operation using persistent client"""
        try:
            client = get_global_client(self.proxy)
            
            network_start = time.time()
            response = await client.post(self.hold_url, content=body_str, headers=headers)
            response_time = (time.time() - start_time) * 1000.0
            
            success = response.status_code == 204
            
            if not success:
                logger.warning(f"Hold failed for seat {seat_id}: {response.status_code} - {response.text[:100]}")
            
            return success, response_time
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000.0
            logger.error(f"Exception holding seat {seat_id}: {str(e)}")
            return False, response_time
    
    def _update_performance_stats(self, success: bool, response_time: float):
        """Update performance statistics with detailed monitoring"""
        self.performance_stats['holds_attempted'] += 1
        if success:
            self.performance_stats['holds_successful'] += 1

        self.performance_stats['last_response_times'].append(response_time)

        # Calculate rolling average
        if self.performance_stats['last_response_times']:
            self.performance_stats['average_response_time'] = sum(
                self.performance_stats['last_response_times']
            ) / len(self.performance_stats['last_response_times'])

        # Performance monitoring - log if exceeding 100ms target
        if response_time > 100.0:
            logger.warning(f"⚠️ Hold response time {response_time:.1f}ms exceeds 100ms target")
        elif response_time > 50.0:
            logger.info(f"🟡 Hold response time {response_time:.1f}ms (target: <100ms)")
        else:
            logger.debug(f"🟢 Hold response time {response_time:.1f}ms (excellent)")

        # Calculate success rate
        success_rate = (self.performance_stats['holds_successful'] /
                       self.performance_stats['holds_attempted']) * 100

        # Emit detailed performance signal
        self.performance_signal.emit({
            'avg_ms': response_time,
            'success': success,
            'operation': 'hold',
            'success_rate': success_rate,
            'total_attempts': self.performance_stats['holds_attempted'],
            'rolling_avg_ms': self.performance_stats['average_response_time']
        })
    
    def renew_token_if_needed(self):
        """Check and renew token if needed"""
        if not self.current_token or not self.token_expire_time:
            self._initialize_token()
            return

        # Renew if less than 5 minutes remaining
        if (time.time() + 300) >= self.token_expire_time:
            self._initialize_token()

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get current performance statistics"""
        if not self.performance_stats['holds_attempted']:
            return {
                'holds_attempted': 0,
                'holds_successful': 0,
                'success_rate': 0.0,
                'average_response_time': 0.0,
                'target_met_percentage': 0.0
            }

        success_rate = (self.performance_stats['holds_successful'] /
                       self.performance_stats['holds_attempted']) * 100

        # Calculate percentage of holds meeting <100ms target
        under_100ms = sum(1 for t in self.performance_stats['last_response_times'] if t < 100.0)
        target_met_percentage = (under_100ms / len(self.performance_stats['last_response_times'])) * 100

        return {
            'holds_attempted': self.performance_stats['holds_attempted'],
            'holds_successful': self.performance_stats['holds_successful'],
            'success_rate': success_rate,
            'average_response_time': self.performance_stats['average_response_time'],
            'target_met_percentage': target_met_percentage,
            'recent_response_times': list(self.performance_stats['last_response_times'])
        }
    
    def cleanup(self):
        """Cleanup resources"""
        global _global_client, _global_executor
        
        # Close global client
        if _global_client and not _global_client.is_closed:
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(_global_client.aclose())
                finally:
                    loop.close()
            except Exception as e:
                logger.error(f"Error closing HTTP client: {e}")
        
        # Shutdown executor
        if _global_executor:
            _global_executor.shutdown(wait=False)
            _global_executor = None


# Alias for backward compatibility
SimpleHoldManager = UltraFastHoldManager
